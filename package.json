{"name": "chatspot", "version": "1.0.0", "description": "ChatSpot Messenger - A full-stack chat application with web and mobile clients", "private": true, "workspaces": ["chatspot-backend", "chatspot-frontend", "chatspot-admin"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install-all": "npm install && cd chatspot-backend && npm install && cd ../chatspot-frontend && npm install && cd ../mobile && npm install && cd ../chatspot-admin && npm install", "backend": "cd chatspot-backend && npm run start:dev", "backend:localhost": "cd chatspot-backend && npm run start:localhost", "frontend": "cd chatspot-frontend && npm run dev", "admin": "cd chatspot-admin && npm run dev", "mobile": "cd mobile && npm start", "mobile:localhost": "cd mobile && npm run start:localhost", "dev:localhost": "concurrently \"npm run backend:localhost\" \"npm run mobile:localhost\"", "dev:web": "concurrently \"npm run backend\" \"npm run frontend\"", "dev:admin": "concurrently \"npm run backend\" \"npm run admin\"", "test:backend": "cd chatspot-backend && npm test", "test:frontend": "cd chatspot-frontend && npm test", "test:mobile": "cd mobile && npm test", "test:all": "concurrently \"npm run test:backend\" \"npm run test:frontend\" \"npm run test:mobile\"", "build:backend": "cd chatspot-backend && npm run build", "build:frontend": "cd chatspot-frontend && npm run build", "build:admin": "cd chatspot-admin && npm run build", "build:all": "npm run build:backend && npm run build:frontend && npm run build:admin", "clean": "rimraf node_modules && rimraf chatspot-backend/node_modules && rimraf chatspot-frontend/node_modules && rimraf mobile/node_modules && rimraf chatspot-admin/node_modules", "create-admin": "cd chatspot-backend && npm run create-admin", "reset-db": "cd chatspot-backend && npm run reset-db"}, "keywords": ["chat", "messenger", "react", "react-native", "<PERSON><PERSON><PERSON>", "monorepo"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}