name: Build Android APK

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: mobile/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('mobile/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install dependencies
        run: |
          cd mobile
          yarn install --frozen-lockfile

      - name: Set up Java & Android SDK
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Grant permission to Gradle wrapper
        run: |
          cd mobile/android
          chmod +x gradlew

      - name: Build APK
        run: |
          cd mobile/android
          ./gradlew assembleRelease

      - name: Upload APK as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: android-apk
          path: mobile/android/app/build/outputs/apk/release/app-release.apk
