import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';

@Entity('users')
export class User {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Username of the user',
    example: 'johndoe',
  })
  @Column({ unique: true })
  username: string;

  @ApiHideProperty() // Hide password from Swagger docs
  @Column()
  password: string;

  @ApiProperty({
    description: 'Whether the user is an admin',
    example: false,
    default: false,
  })
  @Column({ name: 'is_admin', default: false })
  isAdmin: boolean;
}
