import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class RegisterTokenDto {
  @ApiProperty({
    description: 'Username of the user',
    example: 'johndoe',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Firebase Cloud Messaging token',
    example: 'fMqXXEFYQ-GNRaq7tUHXm6:APA91bHqH...',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'Device information',
    example: 'Chrome 98.0.4758.102 on Windows 10',
    required: false,
  })
  @IsString()
  @IsOptional()
  deviceInfo?: string;
}
