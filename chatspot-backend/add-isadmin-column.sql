-- Add isAdmin column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'is_admin'
    ) THEN
        ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT false;
    END IF;

    -- If isadmin column exists (without underscore), migrate data and drop it
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'isadmin'
    ) THEN
        -- Copy data from isadmin to is_admin
        UPDATE users SET is_admin = isadmin;
        -- Drop the old column
        ALTER TABLE users DROP COLUMN isadmin;
    END IF;
END $$;
