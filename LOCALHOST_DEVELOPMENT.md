# Localhost Development Setup

This guide explains how to run the ChatSpot mobile app with a localhost backend for development.

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Android Studio** (for Android development)
3. **Xcode** (for iOS development, macOS only)
4. **React Native CLI** (`npm install -g @react-native-community/cli`)

**Note**: No database setup required! The backend uses SQLite for development.

## Quick Start

### 1. Install Dependencies

```bash
# Install all dependencies for all projects
npm run install-all
```

### 2. Configure Environment

The backend is already configured for localhost development in `chatspot-backend/.env`:

```env
# Database - Using SQLite for development
USE_IN_MEMORY_DB=true
# DATABASE_URL=postgresql://chatuser:chatpassword@localhost:5432/chatdb

# JWT
JWT_SECRET=your_secure_jwt_secret_key
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d

# App
PORT=3002
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# CORS
CORS_ORIGIN=*
```

**SQLite Configuration**: The backend automatically creates a `chatspot-dev.db` file for development. No database setup required!

The mobile app is configured to use localhost URLs in development mode (`mobile/src/utils/env.ts`):

```typescript
export const env: EnvVariables = {
  API_URL: __DEV__
    ? 'http://localhost:3002/'
    : 'https://chatspot-backend-8a7y.onrender.com/',
  WS_URL: __DEV__
    ? 'ws://localhost:3002/'
    : 'wss://chatspot-backend-8a7y.onrender.com/',
  ENV: __DEV__ ? 'development' : 'production',
  DEBUG: __DEV__ || false,
};
```

### 3. Run Development Environment

#### Option A: Run Backend and Mobile Together

```bash
# Start both backend and mobile app
npm run dev:localhost
```

This will:
- Start the backend on `http://localhost:3002`
- Start the React Native Metro bundler
- Clear Metro cache for fresh start

#### Option B: Run Components Separately

```bash
# Terminal 1: Start backend
npm run backend:localhost

# Terminal 2: Start mobile app
npm run mobile:localhost
```

### 4. Run Mobile App on Device/Emulator

#### Android

```bash
# For Android emulator (automatic port forwarding)
cd mobile
npm run android:localhost

# For physical Android device (manual port forwarding required)
adb reverse tcp:3002 tcp:3002
cd mobile
npm run android
```

#### iOS

```bash
# For iOS simulator
cd mobile
npm run ios:localhost

# Or standard iOS run
cd mobile
npm run ios
```

## Network Configuration

### Android Emulator
- Automatically forwards `localhost:3002` to the host machine
- No additional configuration needed

### Android Physical Device
- Requires ADB port forwarding: `adb reverse tcp:3002 tcp:3002`
- Or use your computer's IP address instead of localhost

### iOS Simulator
- Can access `localhost` directly
- No additional configuration needed

### iOS Physical Device
- Replace `localhost` with your computer's IP address in `mobile/src/utils/env.ts`
- Ensure your device and computer are on the same network

## Troubleshooting

### Backend Issues

1. **Port Already in Use**
   ```bash
   # Kill process on port 3002
   lsof -ti:3002 | xargs kill -9
   ```

2. **SQLite Database Issues**
   ```bash
   # Delete the database file to start fresh
   rm chatspot-backend/chatspot-dev.db
   ```

### Mobile App Issues

1. **Metro Bundler Cache Issues**
   ```bash
   cd mobile
   npm run start:localhost  # This clears cache automatically
   ```

2. **Android Connection Issues**
   ```bash
   # Ensure port forwarding is set up
   adb reverse tcp:3002 tcp:3002
   
   # Check ADB devices
   adb devices
   ```

3. **iOS Simulator Issues**
   ```bash
   # Reset iOS simulator
   xcrun simctl erase all
   ```

### Network Issues

1. **Can't Connect to Backend**
   - Verify backend is running on `http://localhost:3002`
   - Check firewall settings
   - For physical devices, use computer's IP instead of localhost

2. **CORS Errors**
   - Backend is configured with `CORS_ORIGIN=*` for development
   - Restart backend if CORS issues persist

## Development Workflow

1. **Start Development Environment**
   ```bash
   npm run dev:localhost
   ```

2. **Make Code Changes**
   - Backend changes trigger automatic restart
   - Mobile changes trigger hot reload

3. **Test on Multiple Platforms**
   ```bash
   # Android
   cd mobile && npm run android:localhost
   
   # iOS
   cd mobile && npm run ios:localhost
   ```

4. **Debug Issues**
   - Backend logs in terminal
   - Mobile logs in React Native debugger
   - Use Reactotron for advanced debugging

## Additional Tools

### Reactotron (Optional)
For advanced mobile debugging:

```bash
cd mobile
npm run android:reactotron  # Sets up port forwarding for Reactotron
```

### Database Management
Use any SQLite client to manage your local database:
- DB Browser for SQLite (free)
- TablePlus
- DBeaver
- SQLiteStudio

The database file is located at `chatspot-backend/chatspot-dev.db`

## Production vs Development

- **Development**: Uses localhost URLs, debug logging enabled
- **Production**: Uses production URLs, optimized builds

The environment is automatically detected using React Native's `__DEV__` flag.

## Next Steps

After setting up localhost development:

1. **Create Admin User**
   ```bash
   npm run create-admin
   ```

2. **Test Authentication**
   - Register a new user in the mobile app
   - Login with created credentials

3. **Test Real-time Features**
   - Send messages between users
   - Test typing indicators
   - Test message delivery status

4. **Test Push Notifications**
   - Configure Firebase (see `mobile/FCM_SETUP.md`)
   - Test notifications in background/foreground
