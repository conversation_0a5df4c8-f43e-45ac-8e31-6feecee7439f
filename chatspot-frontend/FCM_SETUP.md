# Firebase Cloud Messaging (FCM) Setup for Chatspot

This guide explains how to set up Firebase Cloud Messaging for push notifications in the Chatspot application.

## Prerequisites

1. A Google account
2. A Firebase project (create one at [Firebase Console](https://console.firebase.google.com/))

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project"
3. Enter a project name (e.g., "Chatspot Messenger")
4. Follow the setup wizard to create your project

## Step 2: Register Your Web App

1. In the Firebase Console, select your project
2. Click the web icon (</>) to add a web app
3. Enter a nickname for your app (e.g., "Chatspot Web")
4. Check the "Also set up Firebase Hosting" option if you plan to use Firebase Hosting
5. Click "Register app"
6. Copy the Firebase configuration object

## Step 3: Configure Firebase in Chatspot

1. Create a `.env` file in the `chatspot-frontend` directory (copy from `.env.example`)
2. Add your Firebase configuration values to the `.env` file:

```
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
VITE_FIREBASE_APP_ID=your-app-id
```

## Step 4: Set Up Cloud Messaging

1. In the Firebase Console, go to "Project settings"
2. Click the "Cloud Messaging" tab
3. Under "Web configuration", click "Generate key pair" to create a VAPID key
4. Copy the generated key and add it to your `.env` file:

```
VITE_FIREBASE_VAPID_KEY=your-vapid-key
```

## Step 5: Update Firebase Service Worker

1. Open `public/firebase-messaging-sw.js`
2. Update the Firebase configuration with your actual values:

```javascript
firebase.initializeApp({
  apiKey: "YOUR_API_KEY",
  projectId: "YOUR_PROJECT_ID",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID"
});
```

## Step 6: Backend Integration

To send notifications from your backend:

1. Install the Firebase Admin SDK in your backend project:

```bash
npm install firebase-admin
```

2. Initialize the Firebase Admin SDK in your backend:

```javascript
const admin = require('firebase-admin');
const serviceAccount = require('./path-to-service-account-key.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
```

3. Send a notification:

```javascript
const sendNotification = async (token, title, body, data = {}) => {
  try {
    const message = {
      token,
      notification: {
        title,
        body
      },
      data,
      webpush: {
        fcm_options: {
          link: 'https://your-app-url.com/chat'
        }
      }
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent message:', response);
    return response;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};
```

## Testing Notifications

1. Make sure your app is running with HTTPS (required for service workers)
2. Grant notification permission when prompted
3. Check the browser console for FCM token
4. Use the Firebase Console to send a test message to your device

## Troubleshooting

- **Notification permission denied**: The user must grant notification permission. You can check the permission status with `Notification.permission`.
- **Service worker not registering**: Make sure your app is served over HTTPS.
- **Token not generated**: Check browser console for errors and make sure your Firebase configuration is correct.
- **Notifications not showing**: Check if the service worker is registered correctly and if the user has granted notification permission.

## Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Firebase Web Setup](https://firebase.google.com/docs/web/setup)
- [Using the FCM JavaScript API](https://firebase.google.com/docs/cloud-messaging/js/client)
