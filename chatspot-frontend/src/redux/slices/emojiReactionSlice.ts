import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface EmojiReaction {
  emoji: string;
  mood: string;
  timestamp: number;
}

interface EmojiReactionState {
  reactionUsers: Record<string, EmojiReaction>; // username -> reaction data
  emojiReactionsEnabled: boolean;
}

const initialState: EmojiReactionState = {
  reactionUsers: {},
  emojiReactionsEnabled: true
};

const emojiReactionSlice = createSlice({
  name: 'emojiReaction',
  initialState,
  reducers: {
    setUserEmojiReaction: (state, action: PayloadAction<{
      userId: string,
      emoji: string | null,
      mood: string | null
    }>) => {
      const { userId, emoji, mood } = action.payload; // userId is actually username

      if (emoji && mood) {
        // Set the emoji reaction with timestamp
        state.reactionUsers[userId] = {
          emoji,
          mood,
          timestamp: Date.now()
        };
      } else {
        // Remove the user's emoji reaction
        delete state.reactionUsers[userId];
      }
    },

    clearEmojiReactions: (state) => {
      state.reactionUsers = {};
    },

    toggleEmojiReactions: (state) => {
      state.emojiReactionsEnabled = !state.emojiReactionsEnabled;
    },

    setEmojiReactionsEnabled: (state, action: PayloadAction<boolean>) => {
      state.emojiReactionsEnabled = action.payload;
    }
  }
});

// Export actions
export const {
  setUserEmojiReaction,
  clearEmojiReactions,
  toggleEmojiReactions,
  setEmojiReactionsEnabled
} = emojiReactionSlice.actions;

// Export selectors
export const selectReactionUsers = (state: RootState) => state.emojiReaction.reactionUsers;
export const selectEmojiReactionsEnabled = (state: RootState) => state.emojiReaction.emojiReactionsEnabled;

export const selectUserEmojiReaction = (state: RootState, username: string) => {
  const reaction = state.emojiReaction.reactionUsers[username];
  if (!reaction) return null;

  if ((Date.now() - reaction.timestamp) < 10000) {
    return reaction;
  }

  return null;
};

export default emojiReactionSlice.reducer;