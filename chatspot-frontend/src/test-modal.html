<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Modal</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f0f2f5;
      margin: 0;
      padding: 20px;
    }
    
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    .modal-content {
      background-color: white;
      border-radius: 8px;
      width: 95%;
      max-width: 350px;
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .modal-header h3 {
      margin: 0;
      font-size: 1.1rem;
      color: #333333;
    }
    
    .close-button {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #757575;
      padding: 0;
      margin: 0;
      line-height: 1;
    }
    
    .modal-body {
      padding: 16px;
    }
    
    .warning-text {
      font-weight: 600;
      margin-bottom: 12px;
      color: #d32f2f;
    }
    
    .info-text {
      margin-bottom: 0;
      color: #757575;
      font-size: 0.9rem;
      line-height: 1.4;
    }
    
    .modal-footer {
      padding: 12px 16px;
      border-top: 1px solid #e0e0e0;
    }
    
    .modal-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
    
    .cancel-button {
      background-color: #f5f5f5;
      color: #333333;
      border: 1px solid #d0d0d0;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.2s;
      min-width: 100px;
    }
    
    .delete-button {
      background-color: #d32f2f;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.2s;
      min-width: 100px;
    }
    
    .cancel-button:hover:not(:disabled) {
      background-color: #e8e8e8;
    }
    
    .delete-button:hover:not(:disabled) {
      background-color: #b71c1c;
    }
    
    .test-button {
      background-color: #2196f3;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Test Modal</h1>
  <button class="test-button" onclick="showModal()">Show Delete User Modal</button>
  
  <div id="modal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Delete User</h3>
        <button class="close-button" onclick="hideModal()">×</button>
      </div>
      
      <div class="modal-body">
        <p class="warning-text">
          Are you sure you want to delete this user from your contacts?
        </p>
        <p class="info-text">
          This will permanently delete all messages and remove all traces of your conversation with this user. 
          This action cannot be undone and will also remove you from their contacts.
        </p>
      </div>
      
      <div class="modal-footer">
        <div class="modal-buttons">
          <button 
            type="button" 
            class="cancel-button" 
            onclick="hideModal()"
          >
            Cancel
          </button>
          <button 
            type="button" 
            class="delete-button" 
            onclick="deleteUser()"
          >
            Delete User
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    function showModal() {
      document.getElementById('modal').style.display = 'flex';
    }
    
    function hideModal() {
      document.getElementById('modal').style.display = 'none';
    }
    
    function deleteUser() {
      alert('User deleted!');
      hideModal();
    }
  </script>
</body>
</html>
