import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { selectUserEmojiReaction } from '../redux/slices/emojiReactionSlice';
import './UserInfo.css';

interface UserInfoProps {
  userId: string; // This is now actually a username
  className?: string;
  lastMessage?: string; // Optional prop for last message
  status?: string; // Optional prop for status
  showEmojiReactions?: boolean; // Whether to show emoji reactions in room list
  disableEmoji?: boolean; // Whether to disable emoji visibility completely
  onClick?: () => void; // Optional click handler for the user info component
}

const UserInfo: React.FC<UserInfoProps> = ({
  userId,
  className = '',
  lastMessage,
  status,
  showEmojiReactions = true, // Default to true
  disableEmoji = false, // Default to false (emojis are visible)
  onClick
}) => {
  // Check if this user has an active emoji reaction (only if emojis are not disabled)
  const emojiReaction = useSelector((state: RootState) =>
    showEmojiReactions && !disableEmoji ? selectUserEmojiReaction(state, userId) : null
  );


  // Display first letter of username as avatar
  const getAvatarText = () => {
    return userId.charAt(0).toUpperCase();
  };

  // Display username
  const getDisplayName = () => {
    return userId;
  };




  return (
    <div className={`user-info ${className}`}>
      <div className="user-avatar user-avatar-public" onClick={onClick} >
        {getAvatarText()}
      </div>
      <div className="user-details">

        {/* Show emoji reaction if active, otherwise show last message */}
        {emojiReaction ? (
          <div className="room-last-message emoji-reaction-message">
            <span className="emoji-reaction-emoji">{emojiReaction.emoji}</span>
          </div>
        ) : lastMessage && (
          <div className="room-last-message">
            {lastMessage}
          </div>
        )}
                <span className="user-name" onClick={onClick} >{getDisplayName()}</span>


        {/* Conditionally render status if it exists */}
        {status && (
          <span className="chat-contact-status">
            {status === 'Typing' ? (
              <>
                <span className="typing-text">Typing</span>
                <span className="header-typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </span>
              </>
            ) : (
              status
            )}
          </span>
        )}
      </div>
    </div>
  );
};

export default UserInfo;
