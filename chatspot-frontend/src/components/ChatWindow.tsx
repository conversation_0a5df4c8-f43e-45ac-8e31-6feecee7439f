import { useSelector, useDispatch } from 'react-redux';
import { selectConnected, sendMessageRequest, connectRequest } from '../redux/slices/socketSlice';
import { selectUser, selectIsAuthenticated, selectAuthToken } from '../redux/slices/authSlice';
import { selectIsUserTyping } from '../redux/slices/typingSlice';
import { selectUserEmojiReaction } from '../redux/slices/emojiReactionSlice';
import { clearCurrentReceiver, setCurrentReceiver } from '../redux/slices/chatDBSlice';
import './ChatWindow.css';
import { RootState } from '../redux/store';
import React, { useState, useEffect, useRef } from 'react';
import UserInfo from './UserInfo';
import ClearChatModal from './ClearChatModal';
import DeleteUserModal from './DeleteUserModal';
import MessageInput from './MessageInput';
import EnhancedMessageItem from './EnhancedMessageItem';
import { selectCurrentReceiverUsername } from '../redux/slices/chatDBSlice';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'; // <-- This is important!
import { faCoffee } from '@fortawesome/free-solid-svg-icons';  // <-- Import the icons you need
import { faCircleMinus, faRotateRight, faAngleLeft } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from 'react-router-dom'




import images from '../assets/images';
// We don't need to import chatDBService anymore as the socketSaga handles clearing

interface Message {
  id: string;
  room_id: string;
  sender_username: string;
  receiver_username: string;
  message: string;
  type?: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'emoji_reaction'; // Added emoji_reaction type
  timestamp: number;
  status: string;
  is_mine: boolean;
}

type MessageRecord = Record<string, any> | Message;

interface ChatWindowProps {
  messages: MessageRecord[];
  receiverUsername: string | null;
  onClearChat?: () => void;
  onBackToRooms?: () => void;
  onShowProfile?: () => void; // <-- add this
}

const ChatWindow: React.FC<ChatWindowProps> = ({ messages = [], receiverUsername = null, onClearChat, onBackToRooms, onShowProfile }) => {
  const dispatch = useDispatch();
  const connected = useSelector(selectConnected);
  const currentUser = useSelector(selectUser);
  const selectedReceiverUsername = useSelector(selectCurrentReceiverUsername);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [showClearModal, setShowClearModal] = useState<boolean>(false);
  const [clearingChat, setClearingChat] = useState<boolean>(false);
  const [showDeleteUserModal, setShowDeleteUserModal] = useState<boolean>(false);
  const [deletingUser, setDeletingUser] = useState<boolean>(false);
  const [chatCleared, setChatCleared] = useState<boolean>(false);


  const [inputFocused, setInputFocused] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [initialViewportHeight, setInitialViewportHeight] = useState(window.innerHeight);

  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authToken = useSelector(selectAuthToken);





  useEffect(() => {
    const updateVh = () => {
      const vh = (window.visualViewport?.height ?? window.innerHeight) * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    updateVh(); // initial set

    window.visualViewport?.addEventListener('resize', updateVh);
    window.addEventListener('orientationchange', updateVh);

    return () => {
      window.visualViewport?.removeEventListener('resize', updateVh);
      window.removeEventListener('orientationchange', updateVh);
    };
  }, []);






  // // Scroll to bottom when messages change
  // useEffect(() => {
  //   if (messagesEndRef.current) {
  //     messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
  //   }

  //   // Reset chatCleared state if new messages arrive
  //   if (messages.length > 0) {
  //     setChatCleared(false);
  //   }
  // }, [messages]);





  // Get typing status from Redux
  const isTyping = receiverUsername ? useSelector((state: RootState) =>
    selectIsUserTyping(state, receiverUsername)
  ) : false;

  // Get emoji reaction from Redux (for received reactions)
  const emojiReaction = receiverUsername ? useSelector((state: RootState) =>
    selectUserEmojiReaction(state, receiverUsername)
  ) : null;

  // Get our own emoji reaction (for sent reactions)
  const myEmojiReaction = currentUser ? useSelector((state: RootState) =>
    selectUserEmojiReaction(state, currentUser)
  ) : null;

  // Format timestamp if available
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return '';
    }
  };

  // Group messages by date
  const getMessageDate = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.toDateString() === today.toDateString()) {
        return 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, {
          weekday: 'long',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return '';
    }
  };

  // Handle opening the clear chat modal
  const handleOpenClearModal = () => {
    setShowClearModal(true);
  };

  // Handle closing the clear chat modal
  const handleCloseClearModal = () => {
    setShowClearModal(false);
  };

  // Handle clearing the chat
  const handleClearChat = async () => {
    if (!currentUser || !receiverUsername) return;

    try {
      setClearingChat(true);

      // First, send a clear_chat type message to notify the other user to clear their chat
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'Chat cleared',
        messageType: 'clear_chat' // Use the new message type system
      }));

      // The socketSaga will handle clearing the local database when sending a clear_chat message
      // This ensures both sides clear their messages consistently

      // Set chat cleared state to true
      setChatCleared(true);

      // Call the parent component's onClearChat callback if provided
      if (onClearChat) {
        onClearChat();
      }

      setShowClearModal(false);
    } catch (error) {
      console.error('Failed to clear chat:', error);
    } finally {
      setClearingChat(false);
    }
  };

  // Handle opening the delete user modal
  const handleOpenDeleteUserModal = () => {
    setShowDeleteUserModal(true);
  };

  // Handle closing the delete user modal
  const handleCloseDeleteUserModal = () => {
    setShowDeleteUserModal(false);
  };

  // Handle deleting the user
  const handleDeleteUser = async () => {
    if (!currentUser || !receiverUsername) return;

    try {
      setDeletingUser(true);

      // Send a delete_user type message to notify the other user to delete the room
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'User deleted',
        messageType: 'delete_user'
      }));

      // The socketSaga will handle deleting the user room when sending a delete_user message
      // This ensures both sides delete the room consistently

      // Clear the current receiver to close the chat window
      dispatch(clearCurrentReceiver());

      setShowDeleteUserModal(false);
    } catch (error) {
      console.error('Failed to delete user:', error);
    } finally {
      setDeletingUser(false);
    }
  };

  // We no longer show emoji reactions in the status
  const chatStatus = connected
    ? (isTyping ? 'Typing' : 'Online')
    : 'Offline';

  console.log(keyboardHeight, chatStatus)





  console.log(keyboardHeight)



  // Auto-reconnect mechanism
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout | null = null;

    // If authenticated but not connected, try to reconnect
    if (isAuthenticated && authToken && !connected) {
      console.log('Socket disconnected, attempting to reconnect...');
      reconnectTimer = setTimeout(() => {
        console.log('Reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 3000); // Try to reconnect after 3 seconds
    }

    // Clean up timer on unmount
    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
    };
  }, [isAuthenticated, authToken, connected, dispatch]);


  const handleUserClick = () => {
    // Use the onShowProfile callback which will handle both mobile and desktop cases
    if (onShowProfile) {
      onShowProfile();
    }
  };






  return (
    <>
      <div
        className="chat-window"
        style={{
          height: `calc(var(--vh) * 100${keyboardHeight > 0 ? ` - ${keyboardHeight}px` : ''})`,
        }}
      >




        <div className="chat-window-header" >
          {receiverUsername && (
            <>
              {onBackToRooms && (
                <button className="back-button" onClick={onBackToRooms}>
                  <span className="back-button-icon"><FontAwesomeIcon icon={faAngleLeft} size="lg" color="#494040" /></span>
                </button>
              )}
              <UserInfo
                userId={receiverUsername}
                className="chat-contact-info"
                status={chatStatus}
                disableEmoji={true}
                onClick={handleUserClick}
              />
              <div className="chat-header-actions">
                <div className="chat-header-buttons">
                  <button
                    className="clear-chat-btn"
                    onClick={handleOpenClearModal}
                    title="Clear all messages"
                  >
                    <span className="clear-icon"><FontAwesomeIcon icon={faRotateRight} size="xl" color="#494040" /></span>
                  </button>
                  <button
                    className="delete-user-btn"
                    onClick={handleOpenDeleteUserModal}
                    title="Delete this user"
                  >
                    <span className="delete-icon"><FontAwesomeIcon icon={faCircleMinus} size="xl" color="#494040" />
                    </span>
                  </button>
                </div>
              </div>
            </>
          )}
        </div>

        {isAuthenticated && !connected && (
          <div className="connection-status-banner">
            <div className="connection-status-message">
              Reconnecting...
            </div>
          </div>
        )}


        <div className="messages-container">
          {messages.length > 0 ? (
            <div className="messages-list">
              {/* Group and display messages */}
              {messages.map((message, index) => {
                // Skip if message is null or undefined
                if (!message) return null;

                // Get message properties safely
                const messageTimestamp = message.timestamp || Date.now();

                const showDateSeparator =
                  index === 0 ||
                  getMessageDate(messageTimestamp) !== getMessageDate(messages[index - 1]?.timestamp || 0);

                const nextMessage = messages[index + 1];
                const isLastInGroup =
                  !nextMessage ||
                  nextMessage.is_mine !== message.is_mine ||
                  getMessageDate(messageTimestamp) !== getMessageDate(nextMessage?.timestamp || 0);

                return (
                  <React.Fragment key={message.id || `msg-${index}`}>
                    {showDateSeparator && (
                      <div className="message-date-separator">
                        <span>{getMessageDate(messageTimestamp)}</span>
                      </div>
                    )}
                    <EnhancedMessageItem
                      message={message}
                      formatTime={formatTime}
                      isLastInGroup={isLastInGroup}
                    />
                  </React.Fragment>
                );
              })}

              <div ref={messagesEndRef} /> {/* Empty div for scrolling to bottom */}
              {/* Received emoji reaction - using message styling */}
              {emojiReaction && (
                <div className=" message received emoji-reaction-message ">
                  <div className="emoji-message-content ">
                    <p>
                      <span className="emoji-reaction-emoji">{emojiReaction.emoji}</span>
                    </p>
                  </div>
                </div>
              )}

              {/* Sent emoji reaction - using message styling */}
              {myEmojiReaction && (
                <div className="message sent emoji-reaction-message">
                  <div className="emoji-message-content">
                    <p>
                      <span className="emoji-reaction-emoji">{myEmojiReaction.emoji}</span>
                    </p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="no-messages">
              {!receiverUsername
                ? 'Select a contact to view messages'
                : !connected
                  ? 'Connect to a server to receive messages.'
                  : chatCleared
                    ? 'Chat has been cleared'
                    : 'No messages yet. Start the conversation!'}
            </div>
          )}
        </div>



        <div className="message-input-container">
          <MessageInput receiverUsername={selectedReceiverUsername || ''} />
        </div>

        {showClearModal && (
          <ClearChatModal
            onClose={handleCloseClearModal}
            onConfirm={handleClearChat}
            loading={clearingChat}
          />
        )}

        {showDeleteUserModal && (
          <DeleteUserModal
            onClose={handleCloseDeleteUserModal}
            onConfirm={handleDeleteUser}
            loading={deletingUser}
            username={receiverUsername || undefined}
          />
        )}
      </div>
    </>
  );
};

export default ChatWindow;
