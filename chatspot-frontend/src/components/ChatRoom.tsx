import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { selectUser } from '../redux/slices/authSlice';
import { setCurrentReceiver, selectCurrentReceiverUsername } from '../redux/slices/chatDBSlice';
import { chatDBService } from '../database/service';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import ChatWindow from './ChatWindow';
import './ChatRoom.css';

const ChatRoom: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { username } = useParams<{ username: string }>();
  const currentUser = useSelector(selectUser);
  const selectedReceiverUsername = useSelector(selectCurrentReceiverUsername);

  // Use WatermelonDB observables for messages
  const messages = useWatermelonObservable(
    (currentUser && username) ?
      chatDBService.observeMessages(currentUser, username) :
      null,
    []
  );

  // Set the current receiver when the component mounts
  useEffect(() => {
    if (username) {
      dispatch(setCurrentReceiver(username));

      // Mark messages as read when the chat room is opened
      if (currentUser) {
        chatDBService.markMessagesAsRead(currentUser, username)
          .catch(error => console.error('Error marking messages as read:', error));
      }
    }
  }, [username, currentUser, dispatch]);

  // Handle back button click
  const handleBackToRooms = () => {
    dispatch(setCurrentReceiver(null));
    navigate('/chat');
  };

  // Handle showing profile
  const handleShowProfile = () => {
    if (username) {
      navigate(`/profile/${username}`);
    }
  };


  return (
    <div className="chat-room-container">

      <div className="chat-room-content">
        <div className="chat-window-container">
          <ChatWindow
            messages={messages}
            receiverUsername={username || ''}
            onClearChat={() => console.log('Chat cleared')}
            onBackToRooms={handleBackToRooms}
            onShowProfile={handleShowProfile}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatRoom;
