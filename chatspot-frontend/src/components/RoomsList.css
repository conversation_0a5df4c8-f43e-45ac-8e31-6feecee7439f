.rooms-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}



.no-rooms {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: 15px;
  flex: 1;
}

@media (min-width: 768px) {
  .no-rooms {
    padding: 20px;
  }
}

.no-rooms-icon {
  font-size: 36px;
  margin-bottom: 10px;
  color: var(--primary-color);
}

@media (min-width: 768px) {
  .no-rooms-icon {
    font-size: 48px;
    margin-bottom: 15px;
  }
}

.no-rooms p {
  margin: 3px 0;
  font-size: 0.9em;
}

@media (min-width: 768px) {
  .no-rooms p {
    margin: 5px 0;
    font-size: 1em;
  }
}

.no-rooms-hint {
  font-size: 0.8em;
  opacity: 0.7;
}

@media (min-width: 768px) {
  .no-rooms-hint {
    font-size: 0.9em;
  }
}

.rooms {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
}

.room-item {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 72px; /* Ensure touch targets are large enough */
}

@media (min-width: 768px) {
  .room-item {
    padding: 10px 15px;
    min-height: 76px;
  }
}

.room-item:hover {
  background-color: #f5f5f5;
}

.room-item.selected {
  position: relative;
  background-color: var(--tint-color-light);
    border-top: 1ps solid #e9e9e9;
    border-bottom: 1ps solid #e9e9e9;
}
.selected .user-avatar-public {
  background-color: var(--shade-color-two);
  color: var(--tint-color-light);
}

.selected .user-name {
  color: var(--shade-color);
}

.selected .room-time {
  color: var(--primary-color);
}


.room-item.selected::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: block;
}

@media (min-width: 768px) {
  .room-item.selected::after {
    display: none; /* Hide the indicator dot on desktop */
  }
}

.room-item.self-chat {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Room user info styles */

    .room-user-info {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
    }

.room-user-info .user-avatar {
  width: 50px;
  height: 50px;
  font-size: 18px;
}

@media (min-width: 768px) {
  .room-user-info .user-avatar {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}





    .user-details {
      flex: 1;  /* Allow text to take remaining space */
      overflow: hidden;  /* Hide overflow text */
      text-align: left;
    }

    .user-name {
      font-size: 17px;
      color: var(--shade-color-two);
      white-space: nowrap; /* Prevent wrapping */
      overflow: hidden;
      text-overflow: ellipsis;
    }





   .room-details {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .room-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

.room-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.room-time {
  font-size: 1rem;
  color: var(--tint-color);
  white-space: nowrap;
  margin-left: 6px;
}

@media (min-width: 768px) {
  .room-time {
    font-size: 0.8em;
    margin-left: 8px;
  }
}

.unread-indicator {
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75em;
  font-weight: bold;
  margin-bottom: 4px;
  padding: 0 4px;
}

@media (min-width: 768px) {
  .unread-indicator {
    min-width: 22px;
    height: 22px;
    font-size: 0.8em;
  }
}

.room-last-message {
  color: var(--text-secondary);
  font-size: 0.98em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 4px;
  max-width: 220px;
  text-align: left;
}

@media (min-width: 768px) {
  .room-last-message {
    font-size: 0.98em;
    max-width: 250px;
  }
}

/* Mobile-first styles are now the default */

.room-user-info .user-name {
  font-size: 17px;
}

@media (min-width: 768px) {
  .room-user-info .user-name {
    font-size: 0.95rem;
  }
}


