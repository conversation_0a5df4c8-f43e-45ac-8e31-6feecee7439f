.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 15px;
  width: 100%;
}

.auth-form {
  text-align: center;
  max-width: 300px;
  margin: auto;
  border: 1px solid #eee;
  border-radius: 25px; /* Adjusted for more rounded corners */
  margin: 0 auto;
  padding: 40px;
}

@media (min-width: 768px) {
  .auth-form {
    padding: 40px; 
    max-width: 320px;
  }
}

.auth-form h2 {
  margin-top: 0;
  margin-bottom: 35px;
  color: #555;
  text-align: center;
  font-size: 18px;
}

@media (min-width: 768px) {
  .auth-form h2 {
    margin-bottom: 30px;
    font-size: 18px;
  }
}

.auth-error {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px;
  border-radius: 8px; /* Adjusted for softer corner */
  margin-bottom: 16px;
  font-size: 13px;
}

@media (min-width: 768px) {
  .auth-error {
    margin-bottom: 20px;
    font-size: 14px;
  }
}

@media (min-width: 768px) {

}


.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #888; 
  font-size: 0.9rem;
  text-align: left;
}

@media (min-width: 768px) {
  .form-group label {
    margin-bottom: 5px;
    font-size: 14px;
    text-align: left;
  }
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1.5px solid #ddd; /* Lighter border color */
  border-radius: 8px; /* Rounded corners for input fields */
  font-size: 14px;
  min-height: 44px; /* Minimum touch target size */
  height: 50px;
}

.form-group input:focus {
  border-color: var(--primary-color); /* Updated primary color */
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.2); /* Updated focus border */
}

.password-input {
  display: flex;
  position: relative;
  width: 100%;
}

.password-input input {
  flex: 1;
  padding-right: 40px;
}

.toggle-visibility {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 8px;
  min-height: auto; /* Override the default button min-height */
  color: var(--primary-color); /* Updated primary color */
  z-index: 2;
}

.field-error {
  color: #d32f2f;
  font-size: 12px;
  margin-top: 5px;
}

.auth-button {
  width: 100%;
  padding: 12px;
  background-color: var(--primary-color); /* Updated primary color */
  color: white;
  border: none;
  border-radius: 45px; /* Updated to match input fields */
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 15px;
  height: 50px;
}

.auth-button:hover {
  background-color: #E03A2A; /* Darker shade for hover */
}

.auth-button:disabled {
  background-color: #f0a39a;
  cursor: not-allowed;
}

.auth-links {
  margin-top: 16px;
  text-align: center;
  font-size: 13px;
}

@media (min-width: 768px) {
  .auth-links {
    margin-top: 20px;
    font-size: 14px;
  }
}

.auth-links a {
  color: var(--primary-color); /* Updated primary color */
  text-decoration: none;
  font-weight: 500;
}

.auth-links a:hover {
  text-decoration: underline;
}
