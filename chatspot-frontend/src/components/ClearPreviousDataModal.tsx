import React from 'react';
import './Modal.css';

interface ClearPreviousDataModalProps {
  onClose: () => void;
  onConfirm: () => void;
  loading: boolean;
  previousUsername: string;
  newUsername: string;
}

const ClearPreviousDataModal: React.FC<ClearPreviousDataModalProps> = ({
  onClose,
  onConfirm,
  loading,
  previousUsername,
  newUsername
}) => {
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Different Account Detected</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <p className="warning-text">
            You are logging in as <strong>{newUsername}</strong>, but your previous login was as <strong>{previousUsername}</strong>.
          </p>
          <p className="info-text">
            To continue with this new account, we need to clear all previous data. This will delete all messages and conversations from the previous account.
            This action cannot be undone.
          </p>
        </div>

        <div className="modal-footer">
          <div className="modal-buttons">
            <button
              type="button"
              className="cancel-button"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              className="confirm-button"
              onClick={onConfirm}
              disabled={loading}
            >
              {loading ? 'Clearing...' : 'Clear Previous Data & Login'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClearPreviousDataModal;
