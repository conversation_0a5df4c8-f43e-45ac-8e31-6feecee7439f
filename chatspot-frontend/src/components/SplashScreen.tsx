import React, { useEffect, useState } from 'react';
import './SplashScreen.css';

const SplashScreen: React.FC = () => {
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    // Start fade out animation after 1.8 seconds
    const timer = setTimeout(() => {
      setFadeOut(true);
    }, 1800);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`splash-screen ${fadeOut ? 'fade-out' : ''}`}>
      <div className="splash-content">
        <div className="logo-container">
          <div className="chat-icon">
            <div className="chat-bubble"></div>
            <div className="chat-bubble"></div>
          </div>
        </div>
        <h1 className="app-name">ChatSpot</h1>
        <div className="loading-indicator">
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
