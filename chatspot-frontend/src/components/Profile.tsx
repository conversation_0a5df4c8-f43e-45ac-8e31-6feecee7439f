import { useState } from 'react';
import './Settings.css';
import SettingModal from './SettingModal'; // Reusable modal
import ClearDataModal from './ClearDataModal'; // Modal for clearing data
import { useSelector, useDispatch } from 'react-redux';
import { selectUser } from '../redux/slices/authSlice';
import { chatDBService } from '../database/service';
import { selectEmojiReactionsEnabled, setEmojiReactionsEnabled } from '../redux/slices/emojiReactionSlice';
import ClearChatModal from './ClearChatModal';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'; // <-- This is important!
import { faCircleMinus, faRotateRight, faAngleLeft } from '@fortawesome/free-solid-svg-icons';



type SettingType = 'password' | 'recall' | 'notifications' | null;

interface SettingsProps {
  username: string;
  onClose?: () => void;
  onLogout: () => void;
}

const Profile: React.FC<SettingsProps> = ({ onClose, onLogout, username }) => {
  const [modalType, setModalType] = useState<SettingType>(null);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showClearDataModal, setShowClearDataModal] = useState(false);
  const [clearingData, setClearingData] = useState(false);
  const currentUser = useSelector(selectUser);
  const emojiReactionsEnabled = useSelector(selectEmojiReactionsEnabled);
const dispatch = useDispatch();
  const [showClearModal, setShowClearModal] = useState<boolean>(false);


const handleToggleEmojiReactions = () => {
  dispatch(setEmojiReactionsEnabled(!emojiReactionsEnabled));
};



  const handleToggleNotifications = () => {
    const newValue = !notificationsEnabled;
    setNotificationsEnabled(newValue);
    console.log('Saved notifications:', newValue);
  };

  const openModal = (type: SettingType) => setModalType(type);
  const closeModal = () => setModalType(null);

  const handleOpenClearDataModal = () => {
    setShowClearModal(true);
  };

  const handleCloseClearDataModal = () => {
    setShowClearModal(false);
  };

  const handleClearAllData = async () => {
    try {
      setClearingData(true);

      // Call the database service to clear all data
      const success = await chatDBService.clearAllData();

      if (success) {
        console.log('All data cleared successfully');
      } else {
        console.error('Failed to clear data');
      }

      setShowClearModal(false);
    } catch (error) {
      console.error('Error clearing data:', error);
    } finally {
      setClearingData(false);
    }
  };

    // Handle opening the clear chat modal
  const handleOpenClearModal = () => {
    setShowClearModal(true);
  };




  return (
    <div className="settings-window">
      <div className="settings-header">
      {/*<div>PROFILE SETTINGS</div>*/}
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="settings-user-info">
  <div className="user-avatar-large">{username.charAt(0).toUpperCase()}</div>
  <div className="username-display">{username}</div>
</div>

      <div className="setting-group">
      <p>Privacy</p>
      <div className="setting-item">
        <span>Recall Limit</span>
        <div className="material-btn" onClick={() => setModalType('recall')}>
          Set Limit
        </div>
      </div>


      <div className="setting-item">
        <span>Reset Messages </span>

        <button
                    className="clear-chat-btn"
                    onClick={handleOpenClearModal}
                    title="Clear all messages"
                  >
                    <span className="clear-icon"><FontAwesomeIcon icon={faRotateRight} size="xl" color="#494040" /></span> 
                  </button>
      </div>
    </div>


      <div className="setting-group">
      <p>Notifications & Sound</p>

      <div className="setting-item">
        <span>Notifications</span>
          <div className="material-btn" >
        <label className="material-switch">
          <input
            type="checkbox"
            checked={notificationsEnabled}
            onChange={handleToggleNotifications}
          />
          <span className="slider"></span>
        </label>
        </div>
      </div>
      </div>


      <div className="setting-group">
      <p>Personalisation</p>
      <div className="setting-item">
        <span>Live Emoji</span>
          <div className="material-btn" >
        <label className="material-switch">
          <input
            type="checkbox"
                    checked={emojiReactionsEnabled}
        onChange={handleToggleEmojiReactions}
          />
          <span className="slider"></span>
        </label>
        </div>
      </div>
      </div>


      
     <div className="setting-group">
      <p>Safety & Support</p>
       <div className="setting-item">
        <span>Block</span>
        <div className="material-btn" >
        
        </div>
      </div>


      <div className="setting-item">
        <span>Report</span>
        <div className="material-btn" >
     
        </div>
      </div>

       <div className="setting-item">
        <span>Delete User  </span>
        <div className="material-btn danger-btn" >
        </div>
      </div>
  <div className="setting-item" style={{backgroundColor:"transparent"}}>
        <span> </span>
        <div className="material-btn danger-btn" >
        </div>
      </div>

 </div>




      {modalType && (
        <SettingModal type={modalType} onClose={closeModal} />
      )}

      {showClearModal && (
        <ClearDataModal
          onClose={handleCloseClearDataModal}
          onConfirm={handleClearAllData}
          loading={clearingData}
        />
      )}

    </div>
  );
};

export default Profile;
