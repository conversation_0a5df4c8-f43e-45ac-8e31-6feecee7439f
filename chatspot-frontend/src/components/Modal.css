.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-background, white);
  border-radius: 8px;
  width: 95%;
  max-width: 350px;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  animation: modalFadeIn 0.3s ease-out;
}

@media (min-width: 768px) {
  .modal-content {
    width: 90%;
    max-width: 450px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 16px;
  font-size: 17px;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--primary-color, #333);
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 22px;
  color: #757575;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.modal-close-button:hover {
  color: #333;
}

.modal-body {
  padding: 16px;
}

.warning-text {
  font-weight: 600;
  margin-bottom: 12px;
  color: #d32f2f;
}

.info-text {
  margin-bottom: 0;
  color: #757575;
  font-size: 0.9rem;
  line-height: 1.4;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #d0d0d0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.cancel-button:hover:not(:disabled) {
  background-color: #e8e8e8;
}

.clear-chat-button {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.clear-chat-button:hover:not(:disabled) {
  background-color: #b71c1c;
}

.cancel-button:disabled,
.clear-chat-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
