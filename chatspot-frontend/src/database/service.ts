import { database, getRoomId } from './config';
import { Q } from '@nozbe/watermelondb';
import { Chat, MessageType } from './models/Chat';
import { Room } from './models/Room';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

// Get collections
const chatsCollection = database.get<Chat>('chats');
const roomsCollection = database.get<Room>('rooms');

// Database service for chat operations
export const chatDBService = {
  // Initialize the database
  initialize: async (): Promise<boolean> => {
    try {
      // Perform a simple write operation to ensure the database is working
      await database.write(async () => {
        // Just check if we can access the collections
        const chats = await chatsCollection.query().fetch();
        const rooms = await roomsCollection.query().fetch();
        console.log('Database initialized with', chats.length, 'chats and', rooms.length, 'rooms');
      });
      return true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      return false;
    }
  },

  // Clear all messages and room data
  clearAllData: async (): Promise<boolean> => {
    try {
      // Get all chats and rooms
      const chats = await chatsCollection.query().fetch();
      const rooms = await roomsCollection.query().fetch();

      console.log(`Clearing database: ${chats.length} chats and ${rooms.length} rooms`);

      // Delete all records in a single transaction
      await database.write(async () => {
        // Delete all chats
        for (const chat of chats) {
          await chat.destroyPermanently();
        }

        // Delete all rooms
        for (const room of rooms) {
          await room.destroyPermanently();
        }
      });

      console.log('Database cleared successfully');
      return true;
    } catch (error) {
      console.error('Failed to clear database:', error);
      return false;
    }
  },

  // Get or create a room
  getOrCreateRoom: async (currentUserId: string, otherUserId: string): Promise<Room> => {
    try {
      const roomId = getRoomId(currentUserId, otherUserId);

      // Check if room exists
      const existingRoom = await roomsCollection.query(
        Q.where('room_id', roomId),
        Q.where('user_id', otherUserId)
      ).fetch();

      if (existingRoom.length > 0) {
        return existingRoom[0];
      }

      // Create new room using database.write
      const newRoom = await database.write(async () => {
        return await roomsCollection.create(room => {
          room.roomId = roomId;
          room.username = otherUserId;
          room.lastMsg = '';
          room.updated = Date.now();
        });
      });

      return newRoom;
    } catch (error) {
      console.error('Failed to get or create room:', error);
      throw error;
    }
  },

  // Save a new message with type
  saveMessage: async (
    sender: string,
    receiver: string,
    message: string,
    isMine: boolean = true,
    type: MessageType = 'text', // Default type is text
    selectedUser: string
  ): Promise<Chat> => {
    try {
      const roomId = getRoomId(sender, receiver);
      const timestamp = Date.now();

      // Use database.write to perform all operations in a single transaction
      const chatMessage = await database.write(async () => {
        // Create the message
        const chatMessage = await chatsCollection.create(chat => {
          chat.roomId = roomId;
          chat.senderUsername = sender;
          chat.receiverUsername = receiver;
          chat.message = message;
          chat.type = type;
          chat.timestamp = timestamp;
          chat.status = isMine ? 'sending' : 'sent'; // Set initial status to 'sending' for outgoing messages
          chat.isMine = isMine;
        });

        // Only update room for regular text messages
        if (type === 'text') {
          // Update or create the room
          const rooms = await roomsCollection.query(
            Q.where('room_id', roomId),
            Q.where('username', isMine ? receiver : sender)
          ).fetch();

          if (rooms.length > 0) {
            await rooms[0].update(room => {
              room.lastMsg = message;
              room.updated = timestamp;
              // If this is a message received by the user (not sent by them), increment unread count
              console.log('sender === selectedUser', sender === selectedUser)
              if (!isMine && (sender !== selectedUser)) {
                room.unreadCount = (room.unreadCount || 0) + 1;
              }
            });
          } else {
            await roomsCollection.create(room => {
              room.roomId = roomId;
              room.username = isMine ? receiver : sender;
              room.lastMsg = message;
              room.updated = timestamp;
              room.unreadCount = isMine ? 0 : 1; // Set unread count to 1 for received messages
            });
          }
        }

        return chatMessage;
      });

      return chatMessage;
    } catch (error) {
      console.error('Failed to save message:', error);
      throw error;
    }
  },

  // Get all messages for a room
  getMessages: async (userId1: string, userId2: string): Promise<Record<string, any>[]> => {
    try {
      const roomId = getRoomId(userId1, userId2);

      const messages = await chatsCollection.query(
        Q.where('room_id', roomId),
        Q.sortBy('timestamp', Q.asc)
      ).fetch();

      return messages.map(message => message?.toJSON());
    } catch (error) {
      console.error('Failed to get messages:', error);
      return [];
    }
  },

  // Observe messages for a room (reactive) - returns JSON objects
  observeMessages: (userId1: string, userId2: string): Observable<Record<string, any>[]> => {
    const roomId = getRoomId(userId1, userId2);

    return chatsCollection.query(
      Q.where('room_id', roomId),
      Q.sortBy('timestamp', Q.asc)
    ).observe()
  },

  // Get all rooms for a user
  getRooms: async (username: string): Promise<Record<string, any>[]> => {
    try {
      const rooms = await roomsCollection.query(
        Q.or(
          Q.where('username', username),
          Q.where('room_id', Q.like(`%${username}%`))
        ),
        Q.sortBy('updated', Q.desc)
      ).fetch();

      return rooms.map(room => room.toJSON());
    } catch (error) {
      console.error('Failed to get rooms:', error);
      return [];
    }
  },

  // Observe rooms for a user (reactive)
  observeRooms: (username: string): Observable<Record<string, any>[]> => {
    return roomsCollection.query(
      Q.or(
        Q.where('username', username),
        Q.where('room_id', Q.like(`%${username}%`))
      ),
      Q.sortBy('updated', Q.desc)
    ).observe()
    .pipe(
      map(rooms => rooms.map(room => room))
    );
  },

  // Update message status
  updateMessageStatus: async (messageId: string, status: string): Promise<boolean> => {
    try {
      console.log(`Updating message ${messageId} status to ${status}`);

      const message = await chatsCollection.find(messageId);

      await database.write(async () => {
        await message.update(msg => {
          msg.status = status;
        });
      });

      console.log(`Successfully updated message ${messageId} status to ${status}`);
      return true;
    } catch (error) {
      console.error(`Failed to update message ${messageId} status:`, error);
      return false;
    }
  },



  // Delete a message
  deleteMessage: async (messageId: string): Promise<boolean> => {
    try {
      const message = await chatsCollection.find(messageId);

      await database.write(async () => {
        await message.destroyPermanently();
      });

      return true;
    } catch (error) {
      console.error('Failed to delete message:', error);
      return false;
    }
  },

  // Delete all messages in a room and update room info
  clearRoom: async (userId1: string, userId2: string): Promise<boolean> => {
    try {
      const roomId = getRoomId(userId1, userId2);

      const messages = await chatsCollection.query(
        Q.where('room_id', roomId)
      ).fetch();

      // Find all rooms associated with this conversation
      const rooms = await roomsCollection.query(
        Q.where('room_id', roomId)
      ).fetch();

      await database.write(async () => {
        // Delete all messages
        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Update all rooms to show they've been cleared
        for (const room of rooms) {
          await room.update(roomRecord => {
            roomRecord.lastMsg = 'Chat cleared'; // Update the last message
            roomRecord.updated = Date.now(); // Update the timestamp
            roomRecord.unreadCount = 0; // Reset unread count when chat is cleared
          });
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to clear room:', error);
      return false;
    }
  },

  // Delete a user room completely (remove all messages and room entries)
  deleteUserRoom: async (userId1: string, userId2: string): Promise<boolean> => {
    try {
      const roomId = getRoomId(userId1, userId2);

      // Find all messages in this room
      const messages = await chatsCollection.query(
        Q.where('room_id', roomId)
      ).fetch();

      // Find all rooms associated with this conversation
      const rooms = await roomsCollection.query(
        Q.where('room_id', roomId)
      ).fetch();

      await database.write(async () => {
        // Delete all messages
        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Delete all room entries
        for (const room of rooms) {
          await room.destroyPermanently();
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to delete user room:', error);
      return false;
    }
  },

  // Send a clear chat message and update room info
  sendClearChatMessage: async (senderId: string, receiverId: string): Promise<void> => {
    try {
      const roomId = getRoomId(senderId, receiverId);

      // Find all rooms associated with this conversation
      const rooms = await roomsCollection.query(
        Q.where('room_id', roomId)
      ).fetch();

      // Update room info in the same transaction
      await database.write(async () => {
        // Update all rooms to show they've been cleared
        for (const room of rooms) {
          await room.update(roomRecord => {
            roomRecord.lastMsg = 'Chat cleared'; // Update the last message
            roomRecord.updated = Date.now(); // Update the timestamp
            roomRecord.unreadCount = 0; // Reset unread count when chat is cleared
          });
        }
      });

      // Create a special clear_chat message
      // No need to save a message, room updates are sufficient
    } catch (error) {
      console.error('Failed to send clear chat message:', error);
      throw error;
    }
  },

  // Send a typing indicator message - doesn't actually save to database
  sendTypingIndicator: async (senderUsername: string, receiverUsername: string, isTyping: boolean): Promise<any> => {
    try {
      // Just return a mock object with the typing information
      // We don't want to save typing indicators to the database
      return {
        id: 'typing_' + Date.now(),
        roomId: getRoomId(senderUsername, receiverUsername),
        senderUsername: senderUsername,
        receiverUsername: receiverUsername,
        message: isTyping ? 'typing' : 'stopped_typing',
        type: 'typing',
        timestamp: Date.now(),
        status: 'sent',
        isMine: true
      };
    } catch (error) {
      console.error('Failed to send typing indicator:', error);
      throw error;
    }
  },

  // Mark messages as read for a specific room
  markMessagesAsRead: async (currentUser: string, otherUser: string): Promise<void> => {
    try {
      const roomId = getRoomId(currentUser, otherUser);
      console.log(`Marking messages as read in room ${roomId} (currentUser: ${currentUser}, otherUser: ${otherUser})`);

      await database.write(async () => {
        // Update the room to reset unread count
        const rooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', otherUser)
        ).fetch();

        console.log(`Found ${rooms.length} rooms for marking as read`);

        for (const room of rooms) {
          const oldUnreadCount = room.unreadCount || 0;
          console.log(`Updating room ${room.id} unread count from ${oldUnreadCount} to 0`);

          await room.update(r => {
            r.unreadCount = 0;
          });
        }

        // Update messages to mark as read
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId),
          Q.where('receiver_username', currentUser),
          Q.where('status', Q.notEq('read'))
        ).fetch();

        console.log(`Found ${messages.length} messages to mark as read`);

        for (const message of messages) {
          console.log(`Marking message ${message.id} as read (from ${message.senderUsername} to ${message.receiverUsername})`);
          await message.update(m => {
            m.status = 'read';
          });
        }
      });

      console.log(`Successfully marked messages as read in room ${roomId}`);
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
      throw error; // Re-throw to allow proper error handling
    }
  },

  // Get total unread message count for a user
  getUnreadCount: async (username: string): Promise<number> => {
    try {
      const rooms = await roomsCollection.query(
        Q.where('username', username)
      ).fetch();

      return rooms.reduce((total, room) => total + (room.unreadCount || 0), 0);
    } catch (error) {
      console.error('Failed to get unread count:', error);
      return 0;
    }
  }
};
