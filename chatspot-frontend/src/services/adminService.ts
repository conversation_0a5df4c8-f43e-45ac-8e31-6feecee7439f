import api from './api';

// Admin-related API services
export const adminService = {
  // Get all users (admin only)
  getAllUsers: async (): Promise<any[]> => {
    try {
      const response = await api.get('/admin/users');
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Failed to get users';
    }
  },
  
  // Get user by ID (admin only)
  getUserById: async (userId: string): Promise<any> => {
    try {
      const response = await api.get(`/admin/users/${userId}`);
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Failed to get user';
    }
  },
  
  // Create a new user (admin only)
  createUser: async (username: string, password: string, isAdmin: boolean = false): Promise<any> => {
    try {
      const response = await api.post('/admin/users', { username, password, isAdmin });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Failed to create user';
    }
  },
  
  // Update a user (admin only)
  updateUser: async (userId: string, updates: { username?: string; password?: string; isAdmin?: boolean }): Promise<any> => {
    try {
      const response = await api.put(`/admin/users/${userId}`, updates);
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Failed to update user';
    }
  },
  
  // Delete a user (admin only)
  deleteUser: async (userId: string): Promise<any> => {
    try {
      const response = await api.delete(`/admin/users/${userId}`);
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Failed to delete user';
    }
  }
};
