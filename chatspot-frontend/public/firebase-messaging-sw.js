// Firebase Cloud Messaging Service Worker
// This file is required for Firebase Cloud Messaging to work properly

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
// Note: Service workers cannot access import.meta.env, so we use the values directly
// These should match the values in your .env files
const firebaseConfig = {
  apiKey: "AIzaSyDmaDkGkwup19iZ2G_kM7hof5q078yGU-s",
  projectId: "guptmessenger-14966",
  messagingSenderId: "130096585895",
  appId: "1:130096585895:web:39ef91088053ec66d60f82"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  // Customize notification here
  const notificationTitle = payload.notification?.title || 'ChatSpot Messenger';
  const notificationOptions = {
    body: payload.notification?.body || 'New message received',
    icon: '/notification-icon.svg',
    badge: '/notification-icon.svg',
    tag: 'chatspot-notification',
    data: {
      url: payload.data?.url || '/',
      ...payload.data
    },
    actions: [
      {
        action: 'open',
        title: 'Open Chat'
      },
      {
        action: 'close',
        title: 'Dismiss'
      }
    ]
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  if (event.action === 'close') {
    return;
  }

  // Handle notification click - open or focus the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Check if there's already a window/tab open with the target URL
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus();
          }
        }

        // If no window is open, open a new one
        if (clients.openWindow) {
          const urlToOpen = event.notification.data?.url || '/';
          return clients.openWindow(urlToOpen);
        }
      })
  );
});

// Handle push events (for additional customization if needed)
self.addEventListener('push', (event) => {
  // This is handled by Firebase messaging.onBackgroundMessage above
  // but you can add additional logic here if needed
  console.log('[firebase-messaging-sw.js] Push event received');
});
