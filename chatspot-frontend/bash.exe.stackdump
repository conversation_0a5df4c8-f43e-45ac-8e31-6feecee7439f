Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE81230000 ntdll.dll
7FFE80840000 KERNEL32.DLL
7FFE7E410000 KERNELBASE.dll
7FFE800C0000 USER32.dll
7FFE7ED60000 win32u.dll
7FFE811C0000 GDI32.dll
7FFE7E9E0000 gdi32full.dll
7FFE7E340000 msvcp_win.dll
7FFE7EB00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE7FE60000 advapi32.dll
7FFE7F590000 msvcrt.dll
7FFE80010000 sechost.dll
7FFE7E3E0000 bcrypt.dll
7FFE7F2B0000 RPCRT4.dll
7FFE7DA40000 CRYPTBASE.DLL
7FFE7EC20000 bcryptPrimitives.dll
7FFE7EEE0000 IMM32.DLL
