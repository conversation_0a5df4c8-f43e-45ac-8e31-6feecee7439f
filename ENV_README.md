# Environment Configuration

This project uses environment variables for configuration across all components (backend, frontend, and mobile).

## Environment Files in Source Control

All environment files (`.env`, `.env.development`, `.env.production`, etc.) are now included in source control to simplify setup and deployment.

## Available Environment Files

### Backend (`chatspot-backend`)

- `.env`: Default environment variables for the backend

### Frontend (`chatspot-frontend`)

- `.env`: Default environment variables for development
- `.env.development`: Environment variables for development builds
- `.env.staging`: Environment variables for staging builds
- `.env.production`: Environment variables for production builds

### Mobile (`chatspot-mobile`)

- `.env`: Default environment variables for the mobile app

## Environment Variables

### Backend

- `DATABASE_URL`: PostgreSQL database connection string
- `USE_IN_MEMORY_DB`: Whether to use an in-memory database (true/false)
- `PORT`: The port on which the backend server runs
- `NODE_ENV`: The environment (development, production)
- `JWT_SECRET`: Secret key for JWT token generation

### Frontend

- `VITE_API_URL`: The URL of the backend API server
- `VITE_WS_URL`: The URL of the WebSocket server
- `VITE_ENV`: Current environment (development, staging, production)
- `VITE_DEBUG`: Enable debug logging (true/false)

### Mobile

- `EXPO_PUBLIC_API_URL`: The URL of the backend API server
- `EXPO_PUBLIC_WS_URL`: The URL of the WebSocket server
- `EXPO_PUBLIC_ENV`: Current environment (development, staging, production)
- `EXPO_PUBLIC_DEBUG`: Enable debug logging (true/false)

## API Configuration

The production environment uses the following API endpoints:

- API URL: `https://chatspot-backend-8a7y.onrender.com/`
- WebSocket URL: `wss://chatspot-backend-8a7y.onrender.com/`

## Database Configuration

The production environment uses PostgreSQL with the following connection string:
```
postgresql://chatspot_db_user:<EMAIL>/chatspot_db
```

For local development, you can use either:
1. The production database (as configured in the default `.env` files)
2. An in-memory PostgreSQL database (by setting `USE_IN_MEMORY_DB=true` in the backend `.env` file)

## Important Notes

- All environment variables used in the frontend must be prefixed with `VITE_` to be exposed to the client-side code.
- All environment variables used in the mobile app must be prefixed with `EXPO_PUBLIC_` to be exposed to the client-side code.
- Never store sensitive information (like API keys or secrets) in these files if they are not meant to be public.
