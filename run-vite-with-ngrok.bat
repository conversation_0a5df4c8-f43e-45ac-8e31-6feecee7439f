@echo off
echo Starting ChatSpot Frontend with ngrok tunnel...

echo.
echo Step 1: Starting Vite development server in production mode...
echo.

start cmd /k "cd chatspot-frontend && npm run dev:prod"

echo.
echo Waiting for Vite server to start...
timeout /t 10 /nobreak

echo.
echo Step 2: Starting ngrok tunnel to port 5174...
echo.

start cmd /k "ngrok http 5174"

echo.
echo All services started! You can now:
echo - Access the web app locally at: http://localhost:5174
echo - Access the web app through ngrok using the URL shown in the ngrok window
echo.
echo Press any key to close this window...
pause > nul
