/**
 * ChatSpot Mobile App
 * Main application component with Redux and authentication
 */

import React, { useEffect, useState } from 'react';
import { StatusBar, AppState, AppStateStatus } from 'react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import RNBootSplash from 'react-native-bootsplash';

import store, { persistor } from './src/redux/store';
import { useAppDispatch, useAppSelector } from './src/hooks/redux';
import {
  selectIsAuthenticated,
  selectAuthUser
} from './src/redux/slices/authSlice';
import {
  initializeFCM,
  registerTokenWithBackend,
  selectFCMInitialized
} from './src/redux/slices/fcmSlice';
import {
  connectRequest,
  disconnectRequest,
  selectConnected
} from './src/redux/slices/socketSlice';
import { ThemeProvider, useTheme } from './src/theme';

import Login from './src/components/Login';
import Register from './src/components/Register';
import MainScreen from './src/components/MainScreen';
import ChatRoom from './src/components/ChatRoom';
import Profile from './src/components/Profile';
import LoadingScreen from './src/components/LoadingScreen';
import { RootStackParamList } from './src/navigation/types';

const Stack = createStackNavigator<RootStackParamList>();

// Main App Component (inside Redux Provider)
const AppContent: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<'login' | 'register'>('login');
  const [appReady, setAppReady] = useState(false);
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const authUser = useAppSelector(selectAuthUser);
  const fcmInitialized = useAppSelector(selectFCMInitialized);
  const socketConnected = useAppSelector(selectConnected);
  const { isDark, colors } = useTheme();

  // Track app state for socket management
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);

  // Initialize FCM when app starts
  useEffect(() => {
    if (!fcmInitialized) {
      dispatch(initializeFCM() as any);
    }
  }, [dispatch, fcmInitialized]);

  // Register FCM token with backend when user is authenticated
  useEffect(() => {
    if (isAuthenticated && authUser && fcmInitialized) {
      dispatch(registerTokenWithBackend(authUser) as any);
    }
  }, [dispatch, isAuthenticated, authUser, fcmInitialized]);

  // Hide bootsplash when app is ready
  useEffect(() => {
    const hideBootSplash = async () => {
      try {
        // Wait for Redux store to be ready and initial data to load
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Hide the bootsplash with a smooth fade transition
        await RNBootSplash.hide({ 
          fade: true
        });
        
        // Mark app as ready
        setAppReady(true);
      } catch (error) {
        console.error('Error hiding bootsplash:', error);
        // Fallback: mark app as ready even if bootsplash hiding fails
        setAppReady(true);
      }
    };

    hideBootSplash();
  }, []);

  // Handle app state changes for socket management
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('App state changed from', appState, 'to', nextAppState);

      if (isAuthenticated) {
        if (appState.match(/inactive|background/) && nextAppState === 'active') {
          // App came to foreground - reconnect socket if needed
          console.log('App came to foreground - reconnecting socket');
          if (!socketConnected) {
            dispatch(connectRequest() as any);
          }
        } else if (appState === 'active' && nextAppState.match(/inactive|background/)) {
          // App went to background - disconnect socket to allow FCM notifications
          console.log('App went to background - disconnecting socket for FCM');
          if (socketConnected) {
            dispatch(disconnectRequest() as any);
          }
        }
      }

      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [dispatch, isAuthenticated, appState, socketConnected]);

  const navigateToRegister = () => setCurrentScreen('register');
  const navigateToLogin = () => setCurrentScreen('login');

  // Show loading screen while bootsplash is hiding
  if (!appReady) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <SafeAreaView style={{ flex: 1, backgroundColor: isDark ? colors.black : colors.white }}>
        <StatusBar
          barStyle={isDark ? "light-content" : "dark-content"}
          backgroundColor={colors.background}
        />
        {isAuthenticated ? (
          <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name="MainScreen" component={MainScreen} />
            <Stack.Screen name="ChatRoom" component={ChatRoom} />
            <Stack.Screen name="Profile" component={Profile} />
          </Stack.Navigator>
        ) : currentScreen === 'login' ? (
          <Login navigation={{ navigate: navigateToRegister }} />
        ) : (
          <Register navigation={{ navigate: navigateToLogin }} />
        )}
      </SafeAreaView>
    </NavigationContainer>
  );
};

// Root App Component with Providers
const App: React.FC = () => {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <PersistGate loading={<></>} persistor={persistor}>
          <ThemeProvider>
            <AppContent />
          </ThemeProvider>
        </PersistGate>
      </Provider>
    </GestureHandlerRootView>
  );
};

export default App;
