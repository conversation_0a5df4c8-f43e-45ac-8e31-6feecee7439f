/**
 * Storage utilities for React Native using AsyncStorage
 * This replaces localStorage functionality from the web version
 */
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  AUTH_USERNAME: 'auth_username',
  PREVIOUS_USERNAME: 'previous_username',
} as const;

/**
 * Store a value in AsyncStorage
 */
export const setItem = async (key: string, value: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (error) {
    console.error('Error storing data:', error);
    throw error;
  }
};

/**
 * Get a value from AsyncStorage
 */
export const getItem = async (key: string): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(key);
  } catch (error) {
    console.error('Error retrieving data:', error);
    return null;
  }
};

/**
 * Remove a value from AsyncStorage
 */
export const removeItem = async (key: string): Promise<void> => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing data:', error);
    throw error;
  }
};

/**
 * Clear all data from AsyncStorage
 */
export const clearAll = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Error clearing all data:', error);
    throw error;
  }
};

/**
 * Auth-specific storage utilities
 */
export const authStorage = {
  // Access token management
  setToken: (token: string) => setItem(STORAGE_KEYS.AUTH_TOKEN, token),
  getToken: () => getItem(STORAGE_KEYS.AUTH_TOKEN),
  removeToken: () => removeItem(STORAGE_KEYS.AUTH_TOKEN),

  // Refresh token management
  setRefreshToken: (token: string) => setItem(STORAGE_KEYS.REFRESH_TOKEN, token),
  getRefreshToken: () => getItem(STORAGE_KEYS.REFRESH_TOKEN),
  removeRefreshToken: () => removeItem(STORAGE_KEYS.REFRESH_TOKEN),

  // Username management
  setUsername: (username: string) => setItem(STORAGE_KEYS.AUTH_USERNAME, username),
  getUsername: () => getItem(STORAGE_KEYS.AUTH_USERNAME),
  removeUsername: () => removeItem(STORAGE_KEYS.AUTH_USERNAME),

  // Previous username for data conflict detection
  setPreviousUsername: (username: string) => setItem(STORAGE_KEYS.PREVIOUS_USERNAME, username),
  getPreviousUsername: () => getItem(STORAGE_KEYS.PREVIOUS_USERNAME),
  removePreviousUsername: () => removeItem(STORAGE_KEYS.PREVIOUS_USERNAME),

  // Store both tokens at once
  setTokens: async (accessToken: string, refreshToken: string) => {
    await setItem(STORAGE_KEYS.AUTH_TOKEN, accessToken);
    await setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  },

  // Clear all auth data
  clearAll: async () => {
    await removeItem(STORAGE_KEYS.AUTH_TOKEN);
    await removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    await removeItem(STORAGE_KEYS.AUTH_USERNAME);
    await removeItem(STORAGE_KEYS.PREVIOUS_USERNAME);
  },
};

export default {
  setItem,
  getItem,
  removeItem,
  clearAll,
  authStorage,
};
