/**
 * Utility functions for handling user data in React Native
 */
import { authStorage } from './storage';

/**
 * Check if there's existing data for a different user
 * @param newUsername The username of the user trying to log in
 * @returns Object with information about previous user data
 */
export const checkPreviousUserData = async (newUsername: string): Promise<{ 
  hasPreviousData: boolean; 
  previousUsername: string | null;
}> => {
  const previousUsername = await authStorage.getPreviousUsername();
  
  // If there's no previous username stored, or it's the same as the new one,
  // then there's no conflict
  if (!previousUsername || previousUsername === newUsername) {
    return { 
      hasPreviousData: false, 
      previousUsername: null 
    };
  }
  
  // There's a different username stored, which means we have data for a different user
  return { 
    hasPreviousData: true, 
    previousUsername 
  };
};

/**
 * Store the current username as the previous username
 * @param username The username to store
 */
export const storePreviousUsername = async (username: string): Promise<void> => {
  await authStorage.setPreviousUsername(username);
};

/**
 * Clear the stored previous username
 */
export const clearPreviousUsername = async (): Promise<void> => {
  await authStorage.removePreviousUsername();
};
