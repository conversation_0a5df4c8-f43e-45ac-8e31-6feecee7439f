import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  StatusBar,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout, selectAuthUser, selectAuthToken } from '../redux/slices/authSlice';
import {
  setInitialized,
  selectDBInitialized,
  initializeDatabase
} from '../redux/slices/chatDBSlice';
import { connectRequest, disconnectRequest, selectConnected, sendMessageRequest } from '../redux/slices/socketSlice';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import { chatDBService } from '../database/service';
import { messageSyncService } from '../services/messageSync';
import RoomsList from './RoomsList';
import ClearChatConfirmModal from './ClearChatConfirmModal';
import DeleteUserConfirmModal from './DeleteUserConfirmModal';
import NewChatModal from './NewChatModal';
import { MainScreenNavigationProp } from '../navigation/types';
import { shadows, radius, spacing, typography, useTheme } from '../theme';
import { Svg, G, Path } from 'react-native-svg';


interface MainScreenProps {
  navigation: MainScreenNavigationProp;
}

const MainScreen: React.FC<MainScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectAuthUser);
  const authToken = useAppSelector(selectAuthToken);
  const dbInitialized = useAppSelector(selectDBInitialized);
  const connected = useAppSelector(selectConnected);
  const { colors } = useTheme();
  const [selectedUsername, setSelectedUsername] = useState<string | null>(null);
  const [showNewChatModal, setShowNewChatModal] = useState<boolean>(false);
  const [showClearChatModal, setShowClearChatModal] = useState<boolean>(false);
  const [showDeleteUserModal, setShowDeleteUserModal] = useState<boolean>(false);
  const [clearingChat, setClearingChat] = useState<boolean>(false);
  const [deletingUser, setDeletingUser] = useState<boolean>(false);
  const [targetUsername, setTargetUsername] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Use WatermelonDB observables to get rooms
  const rooms = useWatermelonObservable(
    user && dbInitialized ? chatDBService.observeRooms(user) : null,
    []
  );

  // Initialize database on component mount
  useEffect(() => {
    const initDB = async () => {
      try {
        const success = await initializeDatabase();
        dispatch(setInitialized(success));
        if (success) {
          console.log('Database initialized successfully');
        } else {
          console.error('Failed to initialize database');
        }
      } catch (error) {
        console.error('Database initialization error:', error);
        dispatch(setInitialized(false));
      }
    };

    if (!dbInitialized) {
      initDB();
    }
  }, [dispatch, dbInitialized]);

  // Manage socket connection at the app level
  useEffect(() => {
    // Connect to socket when authenticated and database is initialized
    if (user && authToken && dbInitialized) {
      console.log('Connecting to socket from MainScreen component');
      dispatch(connectRequest({ authToken }));
    }

    // Disconnect when the component unmounts or user logs out
    return () => {
      if (connected) {
        console.log('Disconnecting socket from MainScreen component');
        dispatch(disconnectRequest());
      }
    };
  }, [user, authToken, dbInitialized, dispatch]);


  const handleProfilePress = () => {
    // Navigate to profile screen with current user
    if (user) {
      navigation.navigate('Profile', { username: user });
    }
  };

  const handleRefresh = async () => {
    if (!user || refreshing) return;

    try {
      setRefreshing(true);
      console.log('Manual refresh: Syncing messages for user:', user);

      // Sync all messages from server
      await messageSyncService.syncAllMessages(user);

      console.log('Manual refresh: Message sync completed successfully');
    } catch (error) {
      console.error('Manual refresh: Failed to sync messages:', error);
      Alert.alert('Sync Error', 'Failed to sync messages. Please try again.');
    } finally {
      setRefreshing(false);
    }
  };

  const handleRoomSelect = (username: string) => {
    setSelectedUsername(username);
    // Navigate to chat screen with the selected user
    navigation.navigate('ChatRoom', { username });
  };

  const handleNewChat = () => {
    setShowNewChatModal(true);
  };

  const handleCloseModal = () => {
    setShowNewChatModal(false);
  };

  const handleStartChat = (username: string) => {
    // Use the existing room select logic to start a new chat
    handleRoomSelect(username);
    setShowNewChatModal(false);
  };

  const handleClearChat = (username: string) => {
    setTargetUsername(username);
    setShowClearChatModal(true);
  };

  const handleDeleteUser = (username: string) => {
    setTargetUsername(username);
    setShowDeleteUserModal(true);
  };

  const confirmClearChat = async () => {
    if (!user || !targetUsername) return;

    try {
      setClearingChat(true);

      // Send a clear_chat type message to notify the other user to clear their chat
      dispatch(sendMessageRequest({
        receiverUsername: targetUsername,
        messageText: 'Chat cleared',
        messageType: 'clear_chat'
      }));

      setShowClearChatModal(false);
      setTargetUsername(null);
    } catch (error) {
      console.error('Failed to clear chat:', error);
      Alert.alert('Error', 'Failed to clear chat');
    } finally {
      setClearingChat(false);
    }
  };

  const confirmDeleteUser = async () => {
    if (!user || !targetUsername) return;

    try {
      setDeletingUser(true);

      // Send a delete_user type message to notify the other user to delete the room
      dispatch(sendMessageRequest({
        receiverUsername: targetUsername,
        messageText: 'User deleted',
        messageType: 'delete_user'
      }));

      setShowDeleteUserModal(false);
      setTargetUsername(null);
    } catch (error) {
      console.error('Failed to delete user:', error);
      Alert.alert('Error', 'Failed to delete user');
    } finally {
      setDeletingUser(false);
    }
  };

  const getAvatarText = () => {
    return user?.charAt(0).toUpperCase() || '?';
  };

  const styles = createStyles(colors);

  if (!dbInitialized) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Initializing database...</Text>
        </View>
      </SafeAreaView>
    );
  }

function SvgComponent(props) {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width="50"
      height="50"
      viewBox="0 0 454.000000 454.000000"
      {...props}
      fill={colors.toneDark2}
    >
      <Path d="M205 82.6c-23.3 3-43.6 10.4-62 22.7-9.4 6.2-26.9 23.8-33.7 33.7-10.2 15-17.6 32.3-21.5 50.3-3.1 14.6-3.2 42.8 0 56.9 9.3 42 35.4 76.4 72 94.7 34.5 17.3 82.2 17.5 118 .6 5.1-2.4 12.2-6.5 15.8-9 3.6-2.5 6.9-4.5 7.5-4.5.5 0 11.6 6.1 24.7 13.5s24.3 13.5 24.9 13.5c.6 0 4.5-2.5 8.7-5.6 7.3-5.3 7.6-5.7 7.1-8.7-.4-1.8-5.2-26.6-10.8-55.2l-10.3-52-16.2-.3c-10.4-.2-16.2.1-16.2.8 0 .5 3.4 16.7 7.5 36s7.5 35.2 7.5 35.5c0 .6-12.3-6.9-17-10.4-1.9-1.4-6.3-5-9.8-7.9-6.1-5.3-6.4-5.4-7.7-3.5-8.9 12.7-17.1 19.6-31 26.3-11.7 5.6-19.7 7.7-34.3 9.1-18.7 1.8-37.9-1.4-52.5-8.7-39.3-19.7-59.6-63-52.2-111.7 6.1-40.3 34.5-71.6 72.4-79.7 38.3-8.1 78.6 4 98.4 29.6l4.2 5.4 12.2-12.2c6.8-6.8 12.3-12.6 12.3-13.1 0-1.8-19-19.5-25.6-23.9-14.7-9.8-32.1-16.9-49.6-20.3-8.1-1.6-35.5-2.8-42.8-1.9z" />
    </Svg>
  )
}


  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <View style={styles.header}>
  <View style={styles.headerTop}>
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
  <SvgComponent style={styles.logo} />
  <Text style={styles.appName}>Gupt Messenger</Text>
</View>


    <TouchableOpacity style={styles.profileButton} onPress={handleProfilePress}>
      <View style={styles.userAvatar}>
        <Text style={styles.avatarText}>{getAvatarText()}</Text>
      </View>
    </TouchableOpacity>
  </View>
</View>


      <View style={styles.roomsContainer}>
        <RoomsList
          rooms={rooms}
          onRoomSelect={handleRoomSelect}
          selectedUsername={selectedUsername}
          onNewChat={handleNewChat}
          onClearChat={handleClearChat}
          onDeleteUser={handleDeleteUser}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      </View>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.floatingActionButton} onPress={handleNewChat}>
        <Icon name="add" size={28} color={colors.white} />
      </TouchableOpacity>

      <NewChatModal
        isVisible={showNewChatModal}
        onClose={handleCloseModal}
        onStartChat={handleStartChat}
      />

      <ClearChatConfirmModal
        isVisible={showClearChatModal}
        onClose={() => {
          setShowClearChatModal(false);
          setTargetUsername(null);
        }}
        onConfirm={confirmClearChat}
        loading={clearingChat}
        username={targetUsername || undefined}
      />

      <DeleteUserConfirmModal
        isVisible={showDeleteUserModal}
        onClose={() => {
          setShowDeleteUserModal(false);
          setTargetUsername(null);
        }}
        onConfirm={confirmDeleteUser}
        loading={deletingUser}
        username={targetUsername || undefined}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  loadingText: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  header: {
    backgroundColor: colors.cardBackground,
    paddingHorizontal: spacing.md,
    paddingTop: Platform.OS === 'ios' ? spacing.sm : spacing.md, // Proper status bar spacing
    paddingBottom: spacing.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  appName: {
    ...typography.h1,
    fontSize: 20,
    fontFamily: 'Outfit-Bold',
    color: colors.toneDark2,
  },
  profileButton: {
  },
  logo:{
    marginRight:spacing.md
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: radius.round,
    backgroundColor: colors.primaryLight3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: colors.primaryDark1,
    fontSize: 18,
    fontFamily: 'Outfit-Bold',
  },
  floatingActionButton: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.toneDark2,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
    elevation: 8, // For Android shadow
  },
  roomsContainer: {
    flex: 1,
  },
});

export default MainScreen;
