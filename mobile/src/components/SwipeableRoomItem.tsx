import React, { useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
  PanGestureHandlerStateChangeEvent,
  State,
} from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { spacing, radius, useTheme } from '../theme';
import RoomItem from './RoomItem';

interface SwipeableRoomItemProps {
  room: Record<string, any>;
  onPress: (username: string) => void;
  onClearChat: (username: string) => void;
  onDeleteUser: (username: string) => void;
  isSelected?: boolean;
}

const SwipeableRoomItem: React.FC<SwipeableRoomItemProps> = ({
  room,
  onPress,
  onClearChat,
  onDeleteUser,
  isSelected = false
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  const translateX = useRef(new Animated.Value(0)).current;
  const lastOffset = useRef(0);
  const isSwipeOpen = useRef(false);

  const SWIPE_THRESHOLD = 80;
  const ACTION_WIDTH = 80;

  const onGestureEvent = (event: PanGestureHandlerGestureEvent) => {
    const { translationX } = event.nativeEvent;
    
    // Only allow left swipe (negative translation)
    const newTranslateX = Math.min(0, Math.max(-ACTION_WIDTH * 2, lastOffset.current + translationX));
    translateX.setValue(newTranslateX);
  };

  const onHandlerStateChange = (event: PanGestureHandlerStateChangeEvent) => {
    const { state, translationX, velocityX } = event.nativeEvent;

    if (state === State.END) {
      const shouldOpen = Math.abs(translationX) > SWIPE_THRESHOLD || Math.abs(velocityX) > 500;
      const targetValue = shouldOpen ? -ACTION_WIDTH * 2 : 0;
      
      lastOffset.current = targetValue;
      isSwipeOpen.current = shouldOpen;

      Animated.spring(translateX, {
        toValue: targetValue,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  };

  const closeSwipe = () => {
    lastOffset.current = 0;
    isSwipeOpen.current = false;
    
    Animated.spring(translateX, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const handleClearChat = () => {
    closeSwipe();
    onClearChat(room.username);
  };

  const handleDeleteUser = () => {
    closeSwipe();
    onDeleteUser(room.username);
  };

  const handleRoomPress = () => {
    if (isSwipeOpen.current) {
      closeSwipe();
    } else {
      onPress(room.username);
    }
  };

  return (
    <View style={styles.container}>
      {/* Action buttons (behind the room item) */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.clearButton]}
          onPress={handleClearChat}
          activeOpacity={0.7}
        >
          <Icon name="refresh" size={24} color={colors.white} />
          <Text style={styles.actionText}>Clear</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={handleDeleteUser}
          activeOpacity={0.7}
        >
          <Icon name="delete" size={24} color={colors.white} />
          <Text style={styles.actionText}>Delete</Text>
        </TouchableOpacity>
      </View>

      {/* Room item (swipeable) */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        activeOffsetX={[-10, 10]}
        failOffsetY={[-5, 5]}
      >
        <Animated.View
          style={[
            styles.swipeableContent,
            {
              transform: [{ translateX }],
            },
          ]}
        >
          <RoomItem
            room={room}
            onPress={handleRoomPress}
            isSelected={isSelected}
          />
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: colors.background,
  },
  actionsContainer: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  actionButton: {
    width: 80,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
        fontFamily: 'Outfit-Medium',

  },
  clearButton: {
    backgroundColor: colors.tone,
  },
  deleteButton: {
    backgroundColor: colors.toneDark3,
  },
  actionText: {
    color: colors.white,
    fontSize: 12,
    fontFamily: 'Outfit-Medium',
    marginTop: 4,
  },
  swipeableContent: {
    backgroundColor: colors.cardBackground,
    zIndex: 2,
  },
});

export default SwipeableRoomItem;
