import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { spacing, typography, radius, useTheme } from '../theme';
import { imageUtils } from '../theme/imageStyles';

interface RoomItemProps {
  room: Record<string, any>;
  onPress: (username: string) => void;
  isSelected?: boolean;
}

const RoomItem: React.FC<RoomItemProps> = ({ room, onPress, isSelected = false }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      // Show time if within 24 hours
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      // Show day of week if within a week
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      // Show date if older than a week
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const truncateMessage = (message: string, maxLength: number = 50): string => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  return (
    <TouchableOpacity
      style={[styles.container, isSelected && styles.selected]}
      onPress={() => onPress(room.username)}
      activeOpacity={0.7}
    >
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>
            {imageUtils.getInitials(room.username)}
          </Text>
        </View>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <Text style={styles.username} numberOfLines={1}>
            {room.username || 'Unknown User'}
          </Text>
          <Text style={styles.timestamp}>
            {room.updated ? formatTime(room.updated) : ''}
          </Text>
        </View>

        <View style={styles.messageRow}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {room.last_msg || 'No messages yet'}
          </Text>
          {room.unread_count && room.unread_count > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>
                {room.unread_count > 9 ? '9+' : room.unread_count.toString()}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: spacing.md,
    backgroundColor: colors.cardBackground,
  },
  selected: {
    backgroundColor: colors.primaryTint, // Using theme color for selection
  },
  avatarContainer: {
    marginRight: spacing.md,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor:colors.toneLight3,
    // backgroundColor will be set dynamically using imageUtils.getAvatarColor()
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: colors.toneDark1,
    fontSize: 18,
    fontFamily: 'Outfit-Bold',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  username: {
    ...typography.body,
    fontFamily: 'Outfit-Medium',
    color: colors.toneDark2,
    flex: 1,
  },
  timestamp: {
    ...typography.caption,
    color: colors.tone,
    marginLeft: spacing.sm,
  },
  messageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.tone,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: colors.toneDark1, // Using theme danger color
    borderRadius: radius.round,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  unreadText: {
    color: colors.white,
    fontSize: 12,
    fontFamily: 'Outfit-Bold',
  },
});

export default RoomItem;
