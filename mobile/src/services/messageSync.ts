import { message<PERSON><PERSON> } from './api';
import { chatDBService } from '../database/service';
import { debugLog } from '../utils/env';

export interface MessageSyncData {
  id: string;
  sender_username: string;
  receiver_username: string;
  message: string;
  type: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';
  timestamp: string;
  status: 'pending' | 'delivered';
  delivered_at: string | null;
  client_message_id?: string | null;
}

export const messageSyncService = {
  /**
   * Sync all messages from the server to WatermelonDB
   * This replaces the socket-based offline message sync
   */
  syncAllMessages: async (currentUsername: string): Promise<void> => {
    try {
      debugLog('Starting message sync for user:', currentUsername);

      // Fetch all messages from the server
      const serverMessages: MessageSyncData[] = await messageAPI.getAllMessages();
      debugLog(`Fetched ${serverMessages.length} messages from server`);

      if (serverMessages.length === 0) {
        debugLog('No messages to sync');
        return;
      }

      // Process each message
      for (const serverMessage of serverMessages) {
        await messageSyncService.syncSingleMessage(serverMessage, currentUsername);
      }

      debugLog('Message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync messages:', error);
      throw error;
    }
  },

  /**
   * Sync a single message to WatermelonDB
   * Handles both new messages and status updates
   */
  syncSingleMessage: async (serverMessage: MessageSyncData, currentUsername: string): Promise<void> => {
    try {
      const {
        sender_username,
        receiver_username,
        message,
        type,
        status,
        client_message_id,
      } = serverMessage;

      // Determine if this is the current user's message
      const isMine = sender_username === currentUsername;

      // Check if message already exists in local database using client_message_id
      let existingMessage = null;

      if (client_message_id) {
        // Use client_message_id to find existing message (most reliable)
        existingMessage = await chatDBService.getMessageById(client_message_id);
        debugLog(`Looking for message with client_message_id: ${client_message_id}, found: ${existingMessage ? 'yes' : 'no'}`);
      }

      // Skip delivered messages from other users that don't exist locally
      // These were already synced previously, so no need to sync again
      if (status === 'delivered' && !isMine && !existingMessage) {
        debugLog(`Skipping delivered message from other user (${sender_username}) - already synced previously`);
        return;
      }

      if (existingMessage) {
        // Message exists, check if we need to update status
        if (existingMessage.status !== status) {
          debugLog(`Updating message status from ${existingMessage.status} to ${status} for ${isMine ? 'own' : 'other'} message`);
          await chatDBService.updateMessageStatus(existingMessage.id, status);
        }
      } else if( !existingMessage && !isMine) {
        // New message, create it
        debugLog(`Creating new ${isMine ? 'own' : 'other'} message with status ${status}: ${message.substring(0, 50)}...`);
        await chatDBService.saveMessage(
          sender_username,
          receiver_username,
          message,
          isMine,
          type,
          status,
          currentUsername, // selectedUser parameter
        );
      } else {
         debugLog(`Skipping delivered message from other user (${sender_username}) - already synced previously`);
      }
    } catch (error) {
      console.error('Failed to sync single message:', error);
      // Don't throw here to allow other messages to continue syncing
    }
  },

  /**
   * Sync messages for a specific conversation
   */
  syncConversationMessages: async (currentUsername: string, otherUsername: string): Promise<void> => {
    try {
      debugLog(`Syncing conversation messages between ${currentUsername} and ${otherUsername}`);

      // Fetch conversation messages from the server
      const serverMessages: MessageSyncData[] = await messageAPI.getConversationMessages(otherUsername);
      debugLog(`Fetched ${serverMessages.length} conversation messages from server`);

      if (serverMessages.length === 0) {
        debugLog('No conversation messages to sync');
        return;
      }

      // Process each message
      for (const serverMessage of serverMessages) {
        await messageSyncService.syncSingleMessage(serverMessage, currentUsername);
      }

      debugLog('Conversation message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync conversation messages:', error);
      throw error;
    }
  },

  /**
   * Sync only pending messages (for quick updates)
   */
  syncPendingMessages: async (currentUsername: string): Promise<void> => {
    try {
      debugLog('Syncing pending messages for user:', currentUsername);

      // Fetch pending messages from the server
      const pendingMessages: MessageSyncData[] = await messageAPI.getPendingMessages();
      debugLog(`Fetched ${pendingMessages.length} pending messages from server`);

      if (pendingMessages.length === 0) {
        debugLog('No pending messages to sync');
        return;
      }

      // Process each pending message
      for (const pendingMessage of pendingMessages) {
        await messageSyncService.syncSingleMessage(pendingMessage, currentUsername);
      }

      debugLog('Pending message sync completed successfully');
    } catch (error) {
      console.error('Failed to sync pending messages:', error);
      throw error;
    }
  },


};
