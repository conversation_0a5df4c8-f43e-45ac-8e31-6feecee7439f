/**
 * Firebase Cloud Messaging (FCM) Service
 * 
 * This service handles:
 * - FCM token registration and management
 * - Push notification handling
 * - Integration with backend API
 * - Notification permissions
 */

import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { Platform, Alert, PermissionsAndroid } from 'react-native';
import api from './api';
import { debugLog } from '../utils/env';

export interface NotificationData {
  title: string;
  body: string;
  data?: Record<string, string>;
}

class FCMService {
  private fcmToken: string | null = null;
  private isInitialized = false;

  /**
   * Initialize FCM service
   */
  async initialize(): Promise<boolean> {
    try {
      debugLog('Initializing FCM service...');

      // Request permission for notifications
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        debugLog('FCM permission denied');
        return false;
      }

      // Get FCM token
      const token = await this.getFCMToken();
      if (!token) {
        debugLog('Failed to get FCM token');
        return false;
      }

      // Set up message handlers
      this.setupMessageHandlers();

      // Set up token refresh handler
      this.setupTokenRefreshHandler();

      this.isInitialized = true;
      debugLog('FCM service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize FCM service:', error);
      return false;
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // For Android 13+ (API level 33+), request notification permission
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
        return true; // Android < 13 doesn't need explicit permission
      }

      // For iOS
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        Alert.alert(
          'Notification Permission',
          'Please enable notifications in Settings to receive message alerts.',
          [{ text: 'OK' }]
        );
      }

      return enabled;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  /**
   * Get FCM token
   */
  async getFCMToken(): Promise<string | null> {
    try {
      if (this.fcmToken) {
        return this.fcmToken;
      }

      const token = await messaging().getToken();
      this.fcmToken = token;
      debugLog('FCM Token obtained:', token);
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  /**
   * Register FCM token with backend
   */
  async registerTokenWithBackend(username: string): Promise<boolean> {
    try {
      const token = await this.getFCMToken();
      if (!token) {
        console.error('No FCM token available for registration');
        return false;
      }

      const deviceInfo = `${Platform.OS} ${Platform.Version}`;
      
      const response = await api.post('/api/notifications/register-token', {
        userId: username,
        token,
        deviceInfo,
      });

      if (response.data.success) {
        debugLog('FCM token registered successfully with backend');
        return true;
      } else {
        console.error('Failed to register FCM token with backend:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('Error registering FCM token with backend:', error);
      return false;
    }
  }

  /**
   * Setup message handlers for different app states
   */
  private setupMessageHandlers(): void {
    // Handle messages when app is in foreground
    messaging().onMessage(async (remoteMessage) => {
      debugLog('Foreground message received:', remoteMessage);
      this.handleForegroundMessage(remoteMessage);
    });

    // Handle messages when app is in background or quit state
    messaging().onNotificationOpenedApp((remoteMessage) => {
      debugLog('Background message opened app:', remoteMessage);
      this.handleNotificationPress(remoteMessage);
    });

    // Handle messages when app is opened from quit state
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          debugLog('Quit state message opened app:', remoteMessage);
          this.handleNotificationPress(remoteMessage);
        }
      });
  }

  /**
   * Setup token refresh handler
   */
  private setupTokenRefreshHandler(): void {
    messaging().onTokenRefresh((token) => {
      debugLog('FCM token refreshed:', token);
      this.fcmToken = token;
      // TODO: Update token with backend when user is authenticated
    });
  }

  /**
   * Handle foreground messages (show custom notification)
   */
  private handleForegroundMessage(remoteMessage: FirebaseMessagingTypes.RemoteMessage): void {
    const { notification, data } = remoteMessage;
    
    if (notification) {
      // Show custom alert for foreground messages
      Alert.alert(
        notification.title || 'New Message',
        notification.body || 'You have a new message',
        [
          { text: 'Dismiss', style: 'cancel' },
          { 
            text: 'View', 
            onPress: () => this.handleNotificationPress(remoteMessage) 
          },
        ]
      );
    }
  }

  /**
   * Handle notification press (navigate to appropriate screen)
   */
  private handleNotificationPress(remoteMessage: FirebaseMessagingTypes.RemoteMessage): void {
    const { data } = remoteMessage;
    
    debugLog('Handling notification press with data:', data);
    
    // TODO: Navigate to appropriate screen based on notification data
    // This will be implemented when navigation is set up
    if (data?.type === 'message' && data?.chatId) {
      // Navigate to specific chat
      debugLog('Should navigate to chat:', data.chatId);
    } else {
      // Navigate to main chat screen
      debugLog('Should navigate to main chat screen');
    }
  }

  /**
   * Check if FCM service is initialized
   */
  isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get current FCM token
   */
  getCurrentToken(): string | null {
    return this.fcmToken;
  }
}

// Export singleton instance
export const fcmService = new FCMService();
export default fcmService;
