import axios, { AxiosInstance, AxiosError } from 'axios';
import { getApiUrl, debugLog } from '../utils/env';
import { tokenRefreshService } from './tokenRefreshService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import store from '../redux/store';
import { logout } from '../redux/slices/authSlice';

// Get API URL from environment utility
const API_URL = getApiUrl();

// Generate or retrieve device ID
const getDeviceId = async (): Promise<string> => {
  try {
    let deviceId = await AsyncStorage.getItem('device_id');
    if (!deviceId) {
      // Generate a unique device ID
      deviceId = `mobile-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      await AsyncStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  } catch (error) {
    // Fallback if AsyncStorage fails
    return `mobile-fallback-${Date.now()}`;
  }
};

// Create axios instance with default config
const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor to include auth token and device ID in requests
api.interceptors.request.use(
  async (config) => {
    // Get token from Redux store instead of manual storage
    const state = store.getState();
    const token = state.auth.token;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add device ID for single-device authentication
    try {
      const deviceId = await getDeviceId();
      config.headers['x-device-id'] = deviceId;
    } catch (error) {
      // Fallback to a simple device identifier
      const fallbackId = `mobile-${Date.now()}`;
      config.headers['x-device-id'] = fallbackId;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors and token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // Handle 401 errors (token expired)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Get refresh token from Redux store
        const state = store.getState();
        const refreshToken = state.auth.refreshToken;
        
        if (refreshToken) {
          // Attempt to refresh the token
          const result = await tokenRefreshService.handleTokenRefresh(refreshToken);

          if (result) {
            // Update Redux state with new tokens
            store.dispatch({
              type: 'auth/refreshTokenSuccess',
              payload: {
                access_token: result.access_token,
                refresh_token: result.refresh_token
              }
            });

            // Update the authorization header and retry the request
            originalRequest.headers.Authorization = `Bearer ${result.access_token}`;
            return api(originalRequest);
          } else {
            // Refresh failed, logout user and clear state
            debugLog('Token refresh failed, logging out user');
            store.dispatch(logout());
            return Promise.reject(error);
          }
        } else {
          debugLog('No refresh token available, logging out user');
          store.dispatch(logout());
          return Promise.reject(error);
        }
      } catch (refreshError) {
        debugLog('Token refresh error:', refreshError);
        store.dispatch(logout());
        return Promise.reject(error);
      }
    }

    // Handle other error cases
    if (error.response) {
      debugLog('Response error:', error.response.data);
    } else if (error.request) {
      debugLog('Request error:', error.request);
    } else {
      debugLog('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

interface AuthResponse {
  access_token: string;
  refresh_token: string;
  [key: string]: any;
}

// Authentication API services
export const authService = {
  // Login with username and password
  login: async (username: string, password: string): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/login', { username, password });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Login failed';
    }
  },

  // Register with username and password
  register: async (username: string, password: string): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/register', { username, password });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Registration failed';
    }
  }
};

// Message API endpoints
export const messageAPI = {
  // Get all messages for the authenticated user
  getAllMessages: async () => {
    const response = await api.get('/api/messages');
    return response.data;
  },

  // Get messages for a specific conversation
  getConversationMessages: async (otherUsername: string) => {
    const response = await api.get(`/api/messages/conversation/${otherUsername}`);
    return response.data;
  },

  // Get pending messages for the authenticated user
  getPendingMessages: async () => {
    const response = await api.get('/api/messages/pending');
    return response.data;
  },

  // Mark messages as delivered
  markAsDelivered: async (messageIds: string[]) => {
    const response = await api.post('/api/messages/delivered', { messageIds });
    return response.data;
  },

  // Mark messages as read
  markAsRead: async (messageIds: string[]) => {
    const response = await api.post('/api/messages/read', { messageIds });
    return response.data;
  },

  // Delete messages
  deleteMessages: async (messageIds: string[]) => {
    const response = await api.delete('/api/messages', { data: { messageIds } });
    return response.data;
  }
};

// Export the API instance for other services
export default api;
