import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

export type RootStackParamList = {
  MainScreen: undefined;
  ChatRoom: { username: string };
  Profile: { username: string };
};

export type MainScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'MainScreen'
>;

export type ChatRoomNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ChatRoom'
>;

export type ChatRoomRouteProp = RouteProp<RootStackParamList, 'ChatRoom'>;

export type ProfileNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Profile'
>;

export type ProfileRouteProp = RouteProp<RootStackParamList, 'Profile'>;
