import { configureStore } from '@reduxjs/toolkit';
import { runSaga } from 'redux-saga';
import { connectRequest, connectSuccess, connectFailure } from '../redux/slices/socketSlice';
import { socketSaga } from '../redux/sagas/socketSaga';
import socketReducer from '../redux/slices/socketSlice';
import authReducer from '../redux/slices/authSlice';
import chatDBReducer from '../redux/slices/chatDBSlice';
import typingReducer from '../redux/slices/typingSlice';
import emojiReactionReducer from '../redux/slices/emojiReactionSlice';

// Mock socket.io-client
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    disconnect: jest.fn(),
    id: 'mock-socket-id'
  }))
}));

// Mock the database service
jest.mock('../database/service', () => ({
  chatDBService: {
    saveMessage: jest.fn(),
    updateMessageStatus: jest.fn(),
    clearRoom: jest.fn(),
    sendClearChatMessage: jest.fn(),
    deleteUserRoom: jest.fn(),
  }
}));

// Mock environment utilities
jest.mock('../utils/env', () => ({
  getWsUrl: () => 'ws://localhost:3000',
  debugLog: jest.fn(),
  isDevelopment: () => true,
}));

describe('Socket Redux Implementation', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        socket: socketReducer,
        auth: authReducer,
        chatDB: chatDBReducer,
        typing: typingReducer,
        emojiReaction: emojiReactionReducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({ thunk: false }),
    });
  });

  test('should handle connect request action', () => {
    const action = connectRequest({ authToken: 'test-token' });
    store.dispatch(action);

    const state = store.getState();
    expect(state.socket.connecting).toBe(true);
    expect(state.socket.error).toBe(null);
    expect(state.socket.authToken).toBe('test-token');
  });

  test('should handle connect success action', () => {
    const action = connectSuccess({ socketId: 'test-socket-id' });
    store.dispatch(action);

    const state = store.getState();
    expect(state.socket.connected).toBe(true);
    expect(state.socket.connecting).toBe(false);
    expect(state.socket.socket).toBe('test-socket-id');
    expect(state.socket.error).toBe(null);
  });

  test('should handle connect failure action', () => {
    const action = connectFailure('Connection failed');
    store.dispatch(action);

    const state = store.getState();
    expect(state.socket.connected).toBe(false);
    expect(state.socket.connecting).toBe(false);
    expect(state.socket.error).toBe('Connection failed');
  });

  test('should have correct initial state', () => {
    const state = store.getState();
    expect(state.socket.socket).toBe(null);
    expect(state.socket.connected).toBe(false);
    expect(state.socket.connecting).toBe(false);
    expect(state.socket.error).toBe(null);
    expect(state.socket.serverUrl).toBe('ws://localhost:3000');
    expect(state.socket.authToken).toBe('');
    expect(state.socket.messages).toEqual([]);
  });
});
