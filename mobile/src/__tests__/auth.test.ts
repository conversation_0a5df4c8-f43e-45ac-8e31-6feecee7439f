/**
 * Auth functionality tests
 */
import { authStorage } from '../utils/storage';
import { checkPreviousUserData } from '../utils/userDataUtils';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
}));

describe('Auth Storage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should store and retrieve token', async () => {
    const mockToken = 'test-token-123';
    
    // Mock the getItem to return our test token
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    AsyncStorage.getItem.mockResolvedValueOnce(mockToken);
    
    await authStorage.setToken(mockToken);
    const retrievedToken = await authStorage.getToken();
    
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('auth_token', mockToken);
    expect(retrievedToken).toBe(mockToken);
  });

  test('should store and retrieve username', async () => {
    const mockUsername = 'testuser';
    
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    AsyncStorage.getItem.mockResolvedValueOnce(mockUsername);
    
    await authStorage.setUsername(mockUsername);
    const retrievedUsername = await authStorage.getUsername();
    
    expect(AsyncStorage.setItem).toHaveBeenCalledWith('auth_username', mockUsername);
    expect(retrievedUsername).toBe(mockUsername);
  });

  test('should clear all auth data', async () => {
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    
    await authStorage.clearAll();
    
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('auth_token');
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('auth_username');
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('temp_username');
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('temp_password');
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('previous_username');
  });
});

describe('User Data Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should detect no previous data for new user', async () => {
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    AsyncStorage.getItem.mockResolvedValueOnce(null); // No previous username
    
    const result = await checkPreviousUserData('newuser');
    
    expect(result.hasPreviousData).toBe(false);
    expect(result.previousUsername).toBe(null);
  });

  test('should detect no conflict for same user', async () => {
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    AsyncStorage.getItem.mockResolvedValueOnce('sameuser'); // Same username
    
    const result = await checkPreviousUserData('sameuser');
    
    expect(result.hasPreviousData).toBe(false);
    expect(result.previousUsername).toBe(null);
  });

  test('should detect previous data for different user', async () => {
    const AsyncStorage = require('@react-native-async-storage/async-storage');
    AsyncStorage.getItem.mockResolvedValueOnce('olduser'); // Different username
    
    const result = await checkPreviousUserData('newuser');
    
    expect(result.hasPreviousData).toBe(true);
    expect(result.previousUsername).toBe('olduser');
  });
});
