import Reactotron from 'reactotron-react-native';
import { reactotronRedux } from 'reactotron-redux';
// @ts-ignore - Type issues with reactotron-redux-saga
import sagaPlugin from 'reactotron-redux-saga';

const reactotron = Reactotron
  .configure({
    name: 'ChatSpot Mobile',
    host: 'localhost', // Change this to your computer's IP if using a physical device
    port: 9090,
  })
  .useReactNative({
    asyncStorage: false, // there are more options to the async storage.
    networking: {
      // optionally, you can turn it off with false.
      ignoreUrls: /symbolicate/,
    },
    editor: false, // there are more options to editor
    errors: { veto: (stackFrame) => false }, // or turn it off with false
    overlay: false, // just turning off overlay
  })
  .use(reactotronRedux())
  // @ts-ignore - Type issues with reactotron-redux-saga
  .use(sagaPlugin({
    except: ['EFFECT_TRIGGERED', 'EFFECT_RESOLVED', 'EFFECT_REJECTED']
  }))
  .connect();

// Clear Reactotron on each app load during development
if (__DEV__) {
  reactotron.clear?.();
}

export default reactotron;
