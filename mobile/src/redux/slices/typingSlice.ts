import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface TypingState {
  [userId: string]: boolean;
}

const initialState: TypingState = {};

const typingSlice = createSlice({
  name: 'typing',
  initialState,
  reducers: {
    setUserTyping: (state, action: PayloadAction<{ userId: string; isTyping: boolean }>) => {
      const { userId, isTyping } = action.payload;
      if (isTyping) {
        state[userId] = true;
      } else {
        delete state[userId];
      }
    },
    clearAllTyping: (state) => {
      return {};
    },
    clearUserTyping: (state, action: PayloadAction<string>) => {
      const userId = action.payload;
      delete state[userId];
    }
  }
});

export const { setUserTyping, clearAllTyping, clearUserTyping } = typingSlice.actions;

// Selectors
export const selectTypingUsers = (state: RootState) => state.typing;
export const selectIsUserTyping = (userId: string) => (state: RootState) => 
  state.typing[userId] || false;

export default typingSlice.reducer;
