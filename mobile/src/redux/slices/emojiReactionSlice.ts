import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface EmojiReaction {
  emoji: string;
  mood: string;
  timestamp: number;
}

interface EmojiReactionState {
  reactionUsers: { [userId: string]: EmojiReaction };
  emojiReactionsEnabled: boolean;
}

const initialState: EmojiReactionState = {
  reactionUsers: {},
  emojiReactionsEnabled: true,
};

const emojiReactionSlice = createSlice({
  name: 'emojiReaction',
  initialState,
  reducers: {
    setUserEmojiReaction: (state, action: PayloadAction<{
      userId: string,
      emoji: string | null,
      mood: string | null
    }>) => {
      const { userId, emoji, mood } = action.payload; // userId is actually username

      if (emoji && mood) {
        // Set the emoji reaction with timestamp
        state.reactionUsers[userId] = {
          emoji,
          mood,
          timestamp: Date.now()
        };
      } else {
        // Remove the user's emoji reaction
        delete state.reactionUsers[userId];
      }
    },

    clearEmojiReactions: (state) => {
      state.reactionUsers = {};
    },

    toggleEmojiReactions: (state) => {
      state.emojiReactionsEnabled = !state.emojiReactionsEnabled;
    },

    // Legacy actions for backward compatibility
    clearUserEmojiReaction: (state, action: PayloadAction<string>) => {
      const userId = action.payload;
      delete state.reactionUsers[userId];
    },
    clearAllEmojiReactions: (state) => {
      state.reactionUsers = {};
    }
  }
});

export const {
  setUserEmojiReaction,
  clearEmojiReactions,
  toggleEmojiReactions,
  clearUserEmojiReaction,
  clearAllEmojiReactions
} = emojiReactionSlice.actions;

// Selectors
export const selectEmojiReactions = (state: RootState) => state.emojiReaction.reactionUsers;
export const selectEmojiReactionsEnabled = (state: RootState) => state.emojiReaction.emojiReactionsEnabled;
export const selectUserEmojiReaction = (userId: string) => (state: RootState) =>
  state.emojiReaction.reactionUsers[userId] || { emoji: null, mood: null };

export default emojiReactionSlice.reducer;
