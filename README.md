# ChatSpot Messenger

A full-stack chat application with web and mobile clients.

## Project Structure

This is a monorepo containing the following components:

- **chatspot-backend**: Server-side code built with Node.js
- **chatspot-frontend**: Web client built with React
- **chatspot-mobile**: Mobile client built with React Native

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- For mobile development: Expo CLI

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd chatspot
   ```

2. Install dependencies for each project:

   ```
   # Backend
   cd chatspot-backend
   npm install
   
   # Frontend
   cd ../chatspot-frontend
   npm install
   
   # Mobile
   cd ../chatspot-mobile
   npm install
   ```

### Running the Projects

#### Backend

```
cd chatspot-backend
npm start
```

#### Frontend

```
cd chatspot-frontend
npm start
```

#### Mobile

```
cd chatspot-mobile
npm start
```

## Environment Variables

- Backend: Create a `.env` file in the `chatspot-backend` directory
- Frontend: Create a `.env` file in the `chatspot-frontend` directory
- Mobile: Create a `.env` file in the `chatspot-mobile` directory

## API Configuration

- API URL: `https://chatspot-backend-8a7y.onrender.com/`
- WebSocket URL: `wss://chatspot-backend-8a7y.onrender.com/`

## Database

The production environment uses PostgreSQL with the following connection string:
```
postgresql://chatspot_db_user:<EMAIL>/chatspot_db
```

For local development, use in-memory PostgreSQL.

## Mobile App Features

- Uses Shopify Restyle for theming with primary color #F04F3D
- Uses WatermelonDB with SQLite for local data storage
- Username-based messaging system (no user IDs in the UI)
- Avatar feature with first letter of username as default
