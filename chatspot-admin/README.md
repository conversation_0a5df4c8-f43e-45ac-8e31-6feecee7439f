# ChatSpot Admin Panel

This is the admin panel for the ChatSpot messenger application. It provides a user-friendly interface for administrators to manage users, messages, and system settings.

## Features

- **User Management**: Create, view, edit, and delete users
- **Message Management**: Monitor and moderate messages between users
- **Dashboard**: View system statistics and recent activity
- **Settings**: Configure system-wide settings

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- ChatSpot backend server running

### Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file in the root directory with the following content:
   ```
   VITE_API_URL=http://localhost:3001
   ```
   Adjust the URL to match your backend server.

3. Start the development server:
   ```
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

## Building for Production

To build the application for production:

```
npm run build
```

The build artifacts will be stored in the `dist/` directory.

## Deploying to Netlify

This application is configured for easy deployment to Netlify. Follow these steps to deploy:

1. Create a new site on Netlify
2. Connect to your Git repository
3. Configure the build settings:
   - Build command: `cd chatspot-admin && npm install && npm run build`
   - Publish directory: `chatspot-admin/dist`
4. Add the following environment variables in Netlify's site settings:
   - `VITE_API_URL`: URL of your backend API (e.g., `https://chatspot-backend-8a7y.onrender.com/`)
   - `VITE_ENV`: Set to `production`
   - `VITE_DEBUG`: Set to `false`
5. Deploy the site

Alternatively, you can deploy directly from the command line using the Netlify CLI:

```
# Install Netlify CLI if you haven't already
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy the site
cd chatspot-admin
npm run build
netlify deploy --prod --dir=dist
```

## Technologies Used

- React
- TypeScript
- Vite
- Redux Toolkit
- React Router
- Material-UI
- Axios

## Project Structure

- `src/components`: Reusable UI components
- `src/pages`: Page components
- `src/redux`: Redux store, slices, and actions
- `src/services`: API services
- `src/utils`: Utility functions
- `src/types`: TypeScript type definitions

## Integration with ChatSpot Backend

This admin panel is designed to work with the ChatSpot backend API. It requires the backend to have admin-specific endpoints for user and message management.
