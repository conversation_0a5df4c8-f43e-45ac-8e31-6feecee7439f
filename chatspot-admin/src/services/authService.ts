import api from './api';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
}

export interface UserInfo {
  userId: string;
  username: string;
  isAdmin: boolean;
}

// Authentication API services
export const authService = {
  // Login with username and password
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    try {
      const response = await api.post('/auth/login', credentials);
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Login failed';
    }
  },

  // Get current user information
  getCurrentUser: async (): Promise<UserInfo> => {
    try {
      const response = await api.get('/auth/me');
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || error.message || 'Failed to get user information';
    }
  },

  // Store authentication token
  setToken: (token: string): void => {
    localStorage.setItem('token', token);
  },

  // Get authentication token
  getToken: (): string | null => {
    return localStorage.getItem('token');
  },

  // Remove authentication token (logout)
  removeToken: (): void => {
    localStorage.removeItem('token');
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token');
  }
};
