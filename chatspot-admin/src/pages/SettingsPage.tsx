import { Box, Typography, Paper, Switch, FormControlLabel, Divider } from '@mui/material';
import AdminLayout from '../components/AdminLayout';

const SettingsPage = () => {
  return (
    <AdminLayout title="Settings">
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="h5" gutterBottom>
          Admin Settings
        </Typography>
        
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            System Settings
          </Typography>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Enable user registration"
            />
            
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Allow message deletion"
            />
            
            <FormControlLabel
              control={<Switch />}
              label="Require admin approval for new users"
            />
          </Box>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="h6" gutterBottom>
            Notification Settings
          </Typography>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Email notifications for new users"
            />
            
            <FormControlLabel
              control={<Switch />}
              label="Email notifications for reported messages"
            />
          </Box>
          
          <Typography variant="body2" sx={{ mt: 3, fontStyle: 'italic' }}>
            Note: These settings are currently for demonstration purposes only.
          </Typography>
        </Paper>
      </Box>
    </AdminLayout>
  );
};

export default SettingsPage;
