@echo off
echo Starting ChatSpot development environment...

echo.
echo ===== Starting Backend with PostgreSQL database =====
echo.
start cmd /k "cd chatspot-backend && call setup-and-run.bat"

echo.
echo Waiting for backend to start...
timeout /t 10 /nobreak

echo.
echo ===== Starting Frontend =====
echo.
start cmd /k "cd chatspot-frontend && call run-dev.bat"

echo.
echo ===== Starting Mobile App =====
echo.
start cmd /k "cd chatspot-mobile && call run-dev.bat"

echo.
echo All services started! You can now:
echo - Access the web app at: http://localhost:5173
echo - Access the mobile app through Expo
echo - Backend API is running at: http://localhost:3001
echo.
echo Press any key to close this window...
pause > nul
