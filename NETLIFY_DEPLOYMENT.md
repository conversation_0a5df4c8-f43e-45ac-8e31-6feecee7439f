# Netlify Deployment Guide for ChatSpot Frontend

This guide explains how to deploy the ChatSpot frontend to Netlify with proper Firebase Cloud Messaging support.

## Prerequisites

1. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)
2. **Netlify CLI**: Install globally with `npm install -g netlify-cli`
3. **Git Repository**: Your code should be pushed to GitHub/GitLab/Bitbucket

## Deployment Methods

### Method 1: Automatic Deployment (Recommended)

1. **Connect Repository to Netlify**:
   - Go to [Netlify Dashboard](https://app.netlify.com)
   - Click "New site from Git"
   - Connect your repository
   - Select the repository containing your ChatSpot project

2. **Configure Build Settings**:
   - **Base directory**: `chatspot-frontend`
   - **Build command**: `npm install && npm run build`
   - **Publish directory**: `chatspot-frontend/dist`

   **Note**: The root `netlify.toml` file is already configured with these settings.

3. **Set Environment Variables**:
   Go to Site Settings > Environment Variables and add:
   ```
   VITE_API_URL=https://chatspot-backend-8a7y.onrender.com/
   VITE_WS_URL=wss://chatspot-backend-8a7y.onrender.com/
   VITE_ENV=production
   VITE_DEBUG=false
   VITE_FIREBASE_API_KEY=AIzaSyDmaDkGkwup19iZ2G_kM7hof5q078yGU-s
   VITE_FIREBASE_PROJECT_ID=guptmessenger-14966
   VITE_FIREBASE_MESSAGING_SENDER_ID=130096585895
   VITE_FIREBASE_APP_ID=1:130096585895:web:39ef91088053ec66d60f82
   VITE_FIREBASE_VAPID_KEY=BFC3QyghCDe6fKAK-ylvrKupXOCEgToba30wgvP5JUqg0ZAsTrf5a4eOGAPldSOU3ZDfPI_qWWYkTjY7IQt4oHU
   ```

4. **Deploy**: Netlify will automatically build and deploy your site.

### Method 2: Manual Deployment via CLI

1. **Login to Netlify**:
   ```bash
   netlify login
   ```

2. **Build and Deploy**:
   ```bash
   # From project root
   npm run deploy:frontend

   # Or manually:
   cd chatspot-frontend
   npm run build
   netlify deploy --prod --dir=dist
   ```

## Firebase Service Worker Configuration

The `netlify.toml` file in `chatspot-frontend/` includes special headers for service workers:

```toml
# Headers for service workers
[[headers]]
  for = "/firebase-messaging-sw.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Service-Worker-Allowed = "/"
    Cache-Control = "no-cache"
```

This ensures:
- ✅ Correct MIME type for service worker files
- ✅ Proper service worker scope permissions
- ✅ No caching issues with service worker updates

## Troubleshooting

### Firebase Service Worker Issues

1. **MIME Type Error**:
   - Ensure `netlify.toml` headers are configured correctly
   - Check that `firebase-messaging-sw.js` is in the `public/` directory

2. **Service Worker Not Found**:
   - Verify the file exists in `chatspot-frontend/public/firebase-messaging-sw.js`
   - Check build output includes the service worker file

3. **Environment Variables**:
   - Ensure all `VITE_` prefixed variables are set in Netlify
   - Check that Firebase config matches your project

### Build Issues

1. **Build Fails**:
   ```bash
   # Test build locally first
   cd chatspot-frontend
   npm run build
   ```

2. **Missing Dependencies**:
   - Ensure `package.json` includes all required dependencies
   - Check that Node.js version matches (18+)

## Testing the Deployment

1. **Access Your Site**: Visit your Netlify URL
2. **Check Service Worker**: Open browser DevTools > Application > Service Workers
3. **Test Notifications**: Login and check if FCM token is generated
4. **Verify Console**: Look for successful service worker registration

## Custom Domain (Optional)

1. Go to Site Settings > Domain Management
2. Add your custom domain
3. Configure DNS settings as instructed
4. Enable HTTPS (automatic with Netlify)

## Monitoring

- **Build Logs**: Check Netlify dashboard for build status
- **Function Logs**: Monitor any serverless functions
- **Analytics**: Enable Netlify Analytics for usage insights

## Security Considerations

- All environment variables are properly prefixed with `VITE_`
- Firebase configuration is safe to expose (client-side)
- HTTPS is enforced by default on Netlify
- Service workers have proper security headers

## Support

If you encounter issues:
1. Check Netlify build logs
2. Verify environment variables
3. Test locally with `npm run build && npm run preview`
4. Check browser console for errors
