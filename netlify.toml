# Configuration for ChatSpot Frontend deployment
# Build settings are configured in Netlify UI:
# - Package directory: chatspot-frontend
# - Build command: npm --workspace chatspot-frontend run build
# - Publish directory: chatspot-frontend/dist

# Headers for service workers
[[headers]]
  for = "/firebase-messaging-sw.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Service-Worker-Allowed = "/"
    Cache-Control = "no-cache"

[[headers]]
  for = "/service-worker.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Service-Worker-Allowed = "/"
    Cache-Control = "no-cache"

# SPA redirects
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
